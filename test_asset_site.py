#!/usr/bin/env python3
"""
Test Asset Site Information

This script checks what site ID the asset actually belongs to.
"""

import requests
import json

def test_asset_site():
    """Check the actual site ID for the asset."""
    
    base_url = "http://127.0.0.1:5010"
    
    print("🔍 CHECKING ASSET SITE INFORMATION")
    print("=" * 50)
    
    # Test with asset 6468980
    assetnum = "6468980"
    
    try:
        print(f"📋 Checking asset details for: {assetnum}")
        
        response = requests.get(
            f"{base_url}/api/asset/details/{assetnum}",
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📋 Asset Details:")
            print(json.dumps(result, indent=2))
            
            if result.get('success'):
                asset_data = result.get('data', {})
                actual_siteid = asset_data.get('siteid')
                actual_orgid = asset_data.get('orgid')
                
                print(f"\n🎯 ASSET INFORMATION:")
                print(f"   • Asset Number: {asset_data.get('assetnum', 'N/A')}")
                print(f"   • Actual Site ID: {actual_siteid}")
                print(f"   • Actual Org ID: {actual_orgid}")
                print(f"   • Description: {asset_data.get('description', 'N/A')}")
                print(f"   • Status: {asset_data.get('status', 'N/A')}")
                
                if actual_siteid and actual_siteid != 'LCVKWT':
                    print(f"\n⚠️ SITE MISMATCH DETECTED!")
                    print(f"   We've been using: LCVKWT")
                    print(f"   Asset actually belongs to: {actual_siteid}")
                    
                    # Test work order creation with correct site ID
                    print(f"\n🔧 Testing work order creation with correct site ID: {actual_siteid}")
                    
                    form_data = {
                        "assetnum": assetnum,
                        "siteid": actual_siteid,
                        "orgid": actual_orgid or "USARMY",
                        "description": "Test with correct site ID",
                        "worktype": "CM",
                        "priority": "3",
                        "longdescription": "Testing work order creation with the asset's actual site ID",
                        "targstartdate": "2025-07-28T10:00",
                        "createdby": "SOFG118757"
                    }
                    
                    wo_response = requests.post(
                        f"{base_url}/api/asset/create-workorder",
                        json=form_data,
                        timeout=30
                    )
                    
                    if wo_response.status_code == 200:
                        wo_result = wo_response.json()
                        if wo_result.get('success'):
                            print(f"🎉 SUCCESS! Work order created with correct site ID!")
                            print(f"   • Work Order Number: {wo_result.get('wonum', 'N/A')}")
                            print(f"   • Site ID: {wo_result.get('siteid', 'N/A')}")
                        else:
                            print(f"❌ Still failed: {wo_result.get('error', 'Unknown error')}")
                    else:
                        print(f"❌ HTTP Error: {wo_response.status_code}")
                else:
                    print(f"\n✅ Site ID matches: {actual_siteid}")
                
            else:
                print(f"\n❌ Failed to get asset details: {result.get('error', 'Unknown error')}")
                
        else:
            print(f"\n❌ HTTP ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"\n❌ EXCEPTION: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 ASSET SITE CHECK COMPLETE")

if __name__ == "__main__":
    test_asset_site()
