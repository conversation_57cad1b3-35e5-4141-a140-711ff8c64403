#!/usr/bin/env python3
"""
Test Direct Work Order Creation API

This script tests the work order creation API directly to verify it's working.
"""

import requests
import json

def test_direct_wo_creation():
    """Test direct work order creation API call."""
    
    base_url = "http://127.0.0.1:5010"
    
    print("🔧 TESTING DIRECT WORK ORDER CREATION API")
    print("=" * 50)
    
    # Test work order creation data
    form_data = {
        "assetnum": "6468980",
        "siteid": "LCVKWT",
        "orgid": "USARMY",
        "description": "Direct API test work order creation",
        "worktype": "CM",
        "priority": "3",
        "longdescription": "Testing direct work order creation API to verify it works",
        "targstartdate": "2025-07-28T10:00",
        "createdby": "SOFG118757"
    }
    
    print("📝 Form Data:")
    print(json.dumps(form_data, indent=2))
    
    try:
        print("\n🚀 Calling work order creation API...")
        
        response = requests.post(
            f"{base_url}/api/asset/create-workorder",
            json=form_data,
            timeout=30
        )
        
        print(f"📊 Response Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📋 Response Data:")
            print(json.dumps(result, indent=2))
            
            if result.get('success'):
                print("\n🎉 SUCCESS! Work Order Created:")
                print(f"   • Work Order Number: {result.get('wonum', 'N/A')}")
                print(f"   • Asset Number: {result.get('assetnum', 'N/A')}")
                print(f"   • Site ID: {result.get('siteid', 'N/A')}")
                print(f"   • Message: {result.get('message', 'N/A')}")
                
                # Check if data contains additional info
                if 'data' in result:
                    data = result['data']
                    print(f"   • Data WO Number: {data.get('wonum', 'N/A')}")
                    print(f"   • Status: {data.get('status', 'N/A')}")
                
                print("\n✅ CONCLUSION: Work Order Creation API is working correctly!")
                
            else:
                print(f"\n❌ FAILURE: {result.get('error', 'Unknown error')}")
                
        else:
            print(f"\n❌ HTTP ERROR: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"\n❌ EXCEPTION: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 DIRECT API TEST COMPLETE")

if __name__ == "__main__":
    test_direct_wo_creation()
