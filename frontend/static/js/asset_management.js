/**
 * Asset Management JavaScript
 * 
 * Handles search, pagination, and display of asset items
 * with responsive design and theme support.
 */

class AssetManagement {
    constructor() {
        this.currentPage = 0;
        this.totalPages = 0;
        this.currentSearchTerm = '';
        this.currentSiteId = '';
        this.currentStatusFilter = '';
        this.currentTypeFilter = '';
        this.currentLimit = 20;
        this.isSearching = false;
        this.searchTimeout = null;
        this.sortColumn = null;
        this.sortDirection = 'asc';

        this.init();
        this.initializeResponsiveHandling();
    }

    init() {
        console.log('🔧 Initializing Asset Management');

        // Load available sites first
        this.loadAvailableSites();

        // Initialize event listeners
        this.initializeEventListeners();

        // Set initial limit based on screen size
        this.setInitialLimit();

        console.log('✅ Asset Management initialized');
    }

    async loadAvailableSites() {
        try {
            const response = await fetch('/api/inventory/available-sites');
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                this.populateSiteDropdown(result.sites, result.default_site);
                this.currentSiteId = result.default_site;
                console.log(`🏢 ASSET: Loaded ${result.sites.length} sites, default: ${result.default_site}`);
            } else {
                console.error('Failed to load sites:', result.error);
                this.showSiteLoadError();
            }
        } catch (error) {
            console.error('Error loading available sites:', error);
            this.showSiteLoadError();
        }
    }

    populateSiteDropdown(sites, defaultSite) {
        const siteFilter = document.getElementById('assetSiteFilter');

        // Clear existing options
        siteFilter.innerHTML = '';

        // Add "All Sites" option
        const allOption = document.createElement('option');
        allOption.value = '';
        allOption.textContent = 'All Sites';
        siteFilter.appendChild(allOption);

        if (sites.length === 0) {
            const noSitesOption = document.createElement('option');
            noSitesOption.value = '';
            noSitesOption.textContent = 'No sites available';
            siteFilter.appendChild(noSitesOption);
            return;
        }

        // Add sites as options
        sites.forEach(site => {
            const option = document.createElement('option');
            option.value = site.siteid;
            option.textContent = `${site.siteid}${site.description !== site.siteid ? ' - ' + site.description : ''}`;

            // Pre-select the user's default site
            if (site.siteid === defaultSite) {
                option.selected = true;
            }

            siteFilter.appendChild(option);
        });

        console.log(`✅ Loaded ${sites.length} available sites for assets, default: ${defaultSite}`);
    }

    showSiteLoadError() {
        const siteFilter = document.getElementById('assetSiteFilter');
        siteFilter.innerHTML = '<option value="">Error loading sites</option>';
    }

    setInitialLimit() {
        const isMobile = window.innerWidth <= 768;
        const limitSelect = document.getElementById('assetLimitFilter');
        
        if (isMobile) {
            limitSelect.value = '10';
            this.currentLimit = 10;
        } else {
            limitSelect.value = '20';
            this.currentLimit = 20;
        }
    }

    initializeEventListeners() {
        // Search form submission
        const searchForm = document.getElementById('assetManagementSearchForm');
        if (searchForm) {
            searchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.performSearch();
            });
        }

        // Real-time search on input
        const searchInput = document.getElementById('assetSearchTerm');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                clearTimeout(this.searchTimeout);
                this.searchTimeout = setTimeout(() => {
                    if (e.target.value.length >= 2 || e.target.value.length === 0) {
                        this.performSearch();
                    }
                }, 500);
            });
        }

        // Filter changes
        const filters = ['assetSiteFilter', 'assetStatusFilter', 'assetTypeFilter', 'assetLimitFilter'];
        filters.forEach(filterId => {
            const filterElement = document.getElementById(filterId);
            if (filterElement) {
                filterElement.addEventListener('change', () => {
                    this.currentPage = 0; // Reset to first page
                    this.performSearch();
                });
            }
        });

        // Select all checkbox
        const selectAllCheckbox = document.getElementById('selectAllAssetsCheckbox');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleSelectAll(e.target.checked);
            });
        }
    }

    initializeResponsiveHandling() {
        // Handle window resize
        window.addEventListener('resize', () => {
            this.handleResponsiveChanges();
        });

        // Initial responsive setup
        this.handleResponsiveChanges();
    }

    handleResponsiveChanges() {
        const isMobile = window.innerWidth <= 768;
        const tableView = document.getElementById('assetTableView');
        const cardView = document.getElementById('assetCardView');

        if (isMobile) {
            if (tableView) tableView.style.display = 'none';
            if (cardView) cardView.style.display = 'block';
        } else {
            if (tableView) tableView.style.display = 'block';
            if (cardView) cardView.style.display = 'none';
        }
    }

    async performSearch() {
        if (this.isSearching) {
            console.log('🔍 ASSET: Search already in progress, skipping...');
            return;
        }

        this.isSearching = true;
        this.showLoadingState();

        try {
            // Get search parameters
            const searchTerm = document.getElementById('assetSearchTerm')?.value?.trim() || '';
            const siteId = document.getElementById('assetSiteFilter')?.value || '';
            const statusFilter = document.getElementById('assetStatusFilter')?.value || '';
            const typeFilter = document.getElementById('assetTypeFilter')?.value || '';
            const limit = parseInt(document.getElementById('assetLimitFilter')?.value || '20');

            // Update current state
            this.currentSearchTerm = searchTerm;
            this.currentSiteId = siteId;
            this.currentStatusFilter = statusFilter;
            this.currentTypeFilter = typeFilter;
            this.currentLimit = limit;

            console.log(`🔍 ASSET: Searching for "${searchTerm}" in site "${siteId}" with status "${statusFilter}" and type "${typeFilter}"`);

            // Build query parameters
            const params = new URLSearchParams({
                q: searchTerm,
                siteid: siteId,
                status: statusFilter,
                type: typeFilter,
                limit: limit.toString(),
                page: this.currentPage.toString()
            });

            // Make API request
            const response = await fetch(`/api/asset/search?${params}`);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.displaySearchResults(result.assets, result.metadata);
                this.updateResultsInfo(result.metadata);
                this.updatePagination(result.metadata);
            } else {
                this.showError(result.error || 'Search failed');
            }

        } catch (error) {
            console.error('Asset search error:', error);
            this.showError('Failed to search assets. Please try again.');
        } finally {
            this.isSearching = false;
            this.hideLoadingState();
        }
    }

    displaySearchResults(assets, metadata) {
        const isMobile = window.innerWidth <= 768;
        
        if (isMobile) {
            this.displayMobileCards(assets);
        } else {
            this.displayDesktopTable(assets);
        }

        // Show/hide export button
        const exportBtn = document.getElementById('exportAssetsBtn');
        if (exportBtn) {
            exportBtn.style.display = assets.length > 0 ? 'inline-block' : 'none';
        }

        // Show/hide no results message
        const noResults = document.getElementById('assetNoResults');
        if (noResults) {
            noResults.style.display = assets.length === 0 ? 'block' : 'none';
        }
    }

    displayDesktopTable(assets) {
        const tableBody = document.getElementById('assetTableBody');
        if (!tableBody) return;

        if (assets.length === 0) {
            tableBody.innerHTML = '<tr><td colspan="11" class="text-center text-muted">No assets found</td></tr>';
            return;
        }

        tableBody.innerHTML = assets.map(asset => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input asset-checkbox" value="${asset.assetnum}" data-siteid="${asset.siteid}">
                </td>
                <td>
                    <a href="javascript:void(0)" onclick="showAssetDetails('${asset.assetnum}', '${asset.siteid}')" class="text-decoration-none">
                        <strong class="text-primary">${asset.assetnum || '-'}</strong>
                    </a>
                </td>
                <td>
                    <span class="badge bg-secondary">${asset.siteid || '-'}</span>
                </td>
                <td>
                    <div class="text-truncate" style="max-width: 200px;" title="${asset.description || ''}">
                        ${asset.description || '-'}
                    </div>
                </td>
                <td>${this.getStatusBadge(asset.status)}</td>
                <td>
                    <div>
                        <strong>${asset.location || '-'}</strong>
                        ${asset.location_description ? `<br><small class="text-muted">${asset.location_description}</small>` : ''}
                    </div>
                </td>
                <td>${asset.assettag || '-'}</td>
                <td>${asset.serialnum || '-'}</td>
                <td>${asset.model || '-'}</td>
                <td>${this.getTypeBadge(asset)}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="showAssetDetails('${asset.assetnum}', '${asset.siteid}')" title="View Details">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="showRelatedRecords('${asset.assetnum}', '${asset.siteid}')" title="Work Orders & Service Requests">
                            <i class="fas fa-list-alt"></i>
                        </button>
                        <div class="btn-group" role="group">
                            <button class="btn btn-sm btn-outline-warning dropdown-toggle" data-bs-toggle="dropdown" data-bs-auto-close="true" data-bs-boundary="viewport" aria-expanded="false" title="Create Records for Asset">
                                <i class="fas fa-plus"></i>
                                <span class="d-none d-lg-inline ms-1">Create</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header"><i class="fas fa-tools me-1"></i>Work Management</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateWorkOrderModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-wrench me-2 text-warning"></i>Create Work Order
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateServiceRequestModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-headset me-2 text-info"></i>Create Service Request
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-exclamation-circle me-1"></i>Issue Management</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateIncidentModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>Create Incident
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateProblemModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-bug me-2 text-warning"></i>Create Problem
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-cogs me-1"></i>Change Management</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateChangeModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-exchange-alt me-2 text-primary"></i>Create Change
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateReleaseModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-rocket me-2 text-success"></i>Create Release
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-search me-1"></i>Operations</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateInspectionModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-clipboard-check me-2 text-secondary"></i>Create Unscheduled Inspection
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showReportDowntimeModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-clock me-2 text-danger"></i>Report Downtime
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </td>
            </tr>
        `).join('');

        // Initialize dropdowns with proper positioning
        this.initializeDropdowns();
    }

    displayMobileCards(assets) {
        const cardView = document.getElementById('assetCardView');
        if (!cardView) return;

        if (assets.length === 0) {
            cardView.innerHTML = '<div class="text-center text-muted">No assets found</div>';
            return;
        }

        // Store assets for navigation
        this.mobileAssets = assets;
        this.currentMobileIndex = 0;

        // Create single card view with navigation
        this.renderMobileSingleCard();
    }

    renderMobileSingleCard() {
        const cardView = document.getElementById('assetCardView');
        if (!cardView || !this.mobileAssets || this.mobileAssets.length === 0) return;

        const asset = this.mobileAssets[this.currentMobileIndex];
        const totalAssets = this.mobileAssets.length;
        const currentPosition = this.currentMobileIndex + 1;

        cardView.innerHTML = `
            <div class="mobile-single-card-container">
                <!-- Navigation Header -->
                <div class="mobile-nav-header">
                    <div class="mobile-nav-counter">
                        <span class="current-position">${currentPosition}</span>
                        <span class="separator">of</span>
                        <span class="total-assets">${totalAssets}</span>
                    </div>
                    <div class="mobile-nav-buttons">
                        <button class="btn btn-outline-secondary btn-sm" onclick="assetManager.navigateMobileCard(-1)" ${this.currentMobileIndex === 0 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="assetManager.navigateMobileCard(1)" ${this.currentMobileIndex === totalAssets - 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                </div>

                <!-- Asset Card -->
                <div class="mobile-asset-card">
                    <div class="mobile-asset-header">
                        <div class="mobile-asset-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <div class="mobile-asset-info">
                            <!-- Asset Number with Label -->
                            <div class="mobile-header-field">
                                <div class="mobile-header-label">
                                    <i class="fas fa-hashtag"></i>
                                    <span>Asset #</span>
                                </div>
                                <h5 class="mobile-asset-title">${asset.assetnum || 'Unknown Asset'}</h5>
                            </div>

                            <!-- Description with Label -->
                            <div class="mobile-header-field">
                                <div class="mobile-header-label">
                                    <i class="fas fa-file-text"></i>
                                    <span>Description</span>
                                </div>
                                <p class="mobile-asset-description">${asset.description || 'No description'}</p>
                            </div>

                            <!-- Site and Status with Labels -->
                            <div class="mobile-header-badges">
                                <div class="mobile-badge-item">
                                    <div class="mobile-badge-label">
                                        <i class="fas fa-building"></i>
                                        <span>Site</span>
                                    </div>
                                    <span class="badge bg-secondary">${asset.siteid || '-'}</span>
                                </div>
                                <div class="mobile-badge-item">
                                    <div class="mobile-badge-label">
                                        <i class="fas fa-circle"></i>
                                        <span>Status</span>
                                    </div>
                                    ${this.getStatusBadge(asset.status)}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Complete Asset Information -->
                    <div class="mobile-asset-details">
                        <div class="mobile-detail-row">
                            <div class="mobile-detail-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Location</span>
                                    <span class="mobile-detail-value">${asset.location || '-'}</span>
                                </div>
                            </div>
                            <div class="mobile-detail-item">
                                <i class="fas fa-tag"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Asset Tag</span>
                                    <span class="mobile-detail-value">${asset.assettag || '-'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-detail-row">
                            <div class="mobile-detail-item">
                                <i class="fas fa-barcode"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Serial Number</span>
                                    <span class="mobile-detail-value">${asset.serialnum || '-'}</span>
                                </div>
                            </div>
                            <div class="mobile-detail-item">
                                <i class="fas fa-cube"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Model</span>
                                    <span class="mobile-detail-value">${asset.model || '-'}</span>
                                </div>
                            </div>
                        </div>

                        <div class="mobile-detail-row">
                            <div class="mobile-detail-item">
                                <i class="fas fa-layer-group"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Asset Type</span>
                                    <span class="mobile-detail-value">${asset.assettype || asset.type || '-'}</span>
                                </div>
                            </div>
                            <div class="mobile-detail-item">
                                <i class="fas fa-industry"></i>
                                <div class="mobile-detail-content">
                                    <span class="mobile-detail-label">Manufacturer</span>
                                    <span class="mobile-detail-value">${asset.manufacturer || '-'}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="mobile-action-buttons">
                        <button class="btn btn-primary mobile-detail-btn" onclick="showAssetDetails('${asset.assetnum}', '${asset.siteid}')">
                            <i class="fas fa-eye me-1"></i>Details
                        </button>
                        <button class="btn btn-outline-success mobile-action-btn" onclick="showRelatedRecords('${asset.assetnum}', '${asset.siteid}')">
                            <i class="fas fa-list-alt me-1"></i>Work Orders & SRs
                        </button>
                        <div class="btn-group mobile-create-dropdown" role="group">
                            <button class="btn btn-outline-warning dropdown-toggle mobile-action-btn" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-plus me-1"></i>Create
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end">
                                <li><h6 class="dropdown-header"><i class="fas fa-tools me-1"></i>Work Management</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateWorkOrderModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-wrench me-2 text-warning"></i>Create Work Order
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateServiceRequestModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-headset me-2 text-info"></i>Create Service Request
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-exclamation-circle me-1"></i>Issue Management</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateIncidentModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-exclamation-triangle me-2 text-danger"></i>Create Incident
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateProblemModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-bug me-2 text-warning"></i>Create Problem
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-cogs me-1"></i>Change Management</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateChangeModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-exchange-alt me-2 text-primary"></i>Create Change
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateReleaseModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-rocket me-2 text-success"></i>Create Release
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><h6 class="dropdown-header"><i class="fas fa-search me-1"></i>Operations</h6></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showCreateInspectionModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-clipboard-check me-2 text-secondary"></i>Create Unscheduled Inspection
                                </a></li>
                                <li><a class="dropdown-item" href="javascript:void(0)" onclick="showReportDowntimeModalForAsset('${asset.assetnum}', '${asset.siteid}')">
                                    <i class="fas fa-clock me-2 text-danger"></i>Report Downtime
                                </a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    navigateMobileCard(direction) {
        if (!this.mobileAssets) return;

        const newIndex = this.currentMobileIndex + direction;
        if (newIndex >= 0 && newIndex < this.mobileAssets.length) {
            this.currentMobileIndex = newIndex;
            this.renderMobileSingleCard();
        }
    }

    getStatusBadge(status) {
        if (!status) return '<span class="badge bg-secondary">Unknown</span>';

        const statusClass = `status-${status}`;
        return `<span class="badge ${statusClass}">${status}</span>`;
    }

    getTypeBadge(asset) {
        // Try multiple type fields in order of preference
        const type = asset.type || asset.assettype || '';
        if (!type || type.trim() === '') {
            return '<span class="type-badge type-unknown">Unknown</span>';
        }
        return `<span class="type-badge">${type}</span>`;
    }

    updateResultsInfo(metadata) {
        const resultsInfo = document.getElementById('assetResultsInfo');
        if (!resultsInfo) return;

        if (metadata.count === 0) {
            resultsInfo.textContent = 'No assets found';
        } else {
            const start = (metadata.page * this.currentLimit) + 1;
            const end = Math.min(start + metadata.count - 1, metadata.count);
            resultsInfo.textContent = `Showing ${start}-${end} of ${metadata.count} assets (${metadata.load_time?.toFixed(2)}s)`;
        }
    }

    updatePagination(metadata) {
        const paginationContainer = document.getElementById('assetPaginationContainer');
        const pagination = document.getElementById('assetPagination');

        if (!paginationContainer || !pagination) return;

        this.totalPages = metadata.total_pages || 0;

        if (this.totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'block';

        // Build pagination HTML
        let paginationHTML = '';

        // Previous button
        paginationHTML += `
            <li class="page-item ${this.currentPage === 0 ? 'disabled' : ''}">
                <a class="page-link" href="javascript:void(0)" onclick="assetManager.goToPage(${this.currentPage - 1})">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </li>
        `;

        // Page numbers
        const startPage = Math.max(0, this.currentPage - 2);
        const endPage = Math.min(this.totalPages - 1, this.currentPage + 2);

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                    <a class="page-link" href="javascript:void(0)" onclick="assetManager.goToPage(${i})">${i + 1}</a>
                </li>
            `;
        }

        // Next button
        paginationHTML += `
            <li class="page-item ${this.currentPage >= this.totalPages - 1 ? 'disabled' : ''}">
                <a class="page-link" href="javascript:void(0)" onclick="assetManager.goToPage(${this.currentPage + 1})">
                    <i class="fas fa-chevron-right"></i>
                </a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        if (page < 0 || page >= this.totalPages || page === this.currentPage) {
            return;
        }

        this.currentPage = page;
        this.performSearch();
    }

    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.asset-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
        });
    }

    showLoadingState() {
        const loadingIndicator = document.getElementById('assetLoadingIndicator');
        const tableView = document.getElementById('assetTableView');
        const cardView = document.getElementById('assetCardView');
        const noResults = document.getElementById('assetNoResults');

        if (loadingIndicator) loadingIndicator.style.display = 'block';
        if (tableView) tableView.style.display = 'none';
        if (cardView) cardView.style.display = 'none';
        if (noResults) noResults.style.display = 'none';
    }

    hideLoadingState() {
        const loadingIndicator = document.getElementById('assetLoadingIndicator');
        if (loadingIndicator) loadingIndicator.style.display = 'none';

        // Restore responsive view
        this.handleResponsiveChanges();
    }

    showError(message) {
        console.error('Asset Management Error:', message);

        const errorDiv = document.getElementById('assetErrorMessage');
        if (errorDiv) {
            errorDiv.querySelector('span').textContent = message;
            errorDiv.style.display = 'block';

            // Auto-hide after 8 seconds
            setTimeout(() => {
                errorDiv.style.display = 'none';
            }, 8000);
        } else {
            // Fallback to alert if element not found
            alert(`Error: ${message}`);
        }
    }

    showLoading(message = 'Loading...') {
        const loadingDiv = document.getElementById('assetLoadingMessage');
        if (loadingDiv) {
            loadingDiv.textContent = message;
            loadingDiv.style.display = 'block';
        }
    }

    hideLoading() {
        const loadingDiv = document.getElementById('assetLoadingMessage');
        if (loadingDiv) {
            loadingDiv.style.display = 'none';
        }
    }

    async clearAssetCache() {
        try {
            const response = await fetch('/api/asset/cache/clear', {
                method: 'POST'
            });

            if (response.ok) {
                console.log('✅ Asset cache cleared');
                // Refresh current search
                this.performSearch();
            } else {
                console.error('Failed to clear asset cache');
            }
        } catch (error) {
            console.error('Error clearing asset cache:', error);
        }
    }

    async showAssetDetails(assetnum, siteid) {
        try {
            console.log(`🔍 Loading asset details for ${assetnum} in site ${siteid}`);

            // Show loading indicator
            this.showLoading('Loading asset details...');

            const response = await fetch(`/api/asset/details/${assetnum}?siteid=${siteid}`);

            console.log(`📡 Asset details response status: ${response.status}`);

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`❌ Asset details HTTP error: ${response.status} - ${errorText}`);
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`📦 Asset details result:`, result);

            this.hideLoading();

            if (result.success && result.asset) {
                this.displayAssetDetailModal(result.asset);
            } else {
                console.error(`❌ Asset details failed:`, result);
                this.showError(result.error || 'Asset not found or no data available');
            }

        } catch (error) {
            console.error('❌ Error loading asset details:', error);
            this.hideLoading();
            this.showError(`Failed to load asset details: ${error.message}`);
        }
    }

    displayAssetDetailModal(asset) {
        const modal = new bootstrap.Modal(document.getElementById('assetDetailModal'));
        const modalTitle = document.getElementById('assetDetailModalLabel');
        const modalBody = document.getElementById('assetDetailModalBody');

        // Store current asset for navigation
        this.currentDetailAsset = asset;
        this.currentDetailIndex = this.findAssetIndex(asset);

        // Set modal title with navigation
        modalTitle.innerHTML = this.createModalTitleWithNavigation(asset);

        // Create both mobile and desktop views
        const isMobile = window.innerWidth <= 768;
        const mobileView = this.createMobileTabbedView(asset);
        const desktopView = this.createDesktopCardView(asset);

        modalBody.innerHTML = `
            <div class="mobile-view d-md-none">
                ${mobileView}
            </div>
            <div class="desktop-view d-none d-md-block">
                ${desktopView}
            </div>
        `;

        modal.show();
    }

    findAssetIndex(asset) {
        if (!this.mobileAssets || !asset) return -1;
        return this.mobileAssets.findIndex(a => a.assetnum === asset.assetnum && a.siteid === asset.siteid);
    }

    createModalTitleWithNavigation(asset) {
        const currentIndex = this.currentDetailIndex;
        const totalAssets = this.mobileAssets ? this.mobileAssets.length : 0;
        const position = currentIndex >= 0 ? currentIndex + 1 : 1;

        const isMobile = window.innerWidth <= 768;

        if (isMobile && totalAssets > 1) {
            return `
                <div class="modal-title-with-nav">
                    <div class="modal-nav-controls">
                        <button class="btn btn-outline-light btn-sm modal-nav-btn" onclick="assetManager.navigateDetailAsset(-1)" ${currentIndex <= 0 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <div class="modal-nav-counter">
                            <span class="current-asset">${position}</span>
                            <span class="separator">of</span>
                            <span class="total-assets">${totalAssets}</span>
                        </div>
                        <button class="btn btn-outline-light btn-sm modal-nav-btn" onclick="assetManager.navigateDetailAsset(1)" ${currentIndex >= totalAssets - 1 ? 'disabled' : ''}>
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div class="modal-asset-title">
                        <i class="fas fa-cogs me-2"></i>Asset Details - ${asset.assetnum || 'Unknown'}
                    </div>
                </div>
            `;
        } else {
            return `<i class="fas fa-cogs me-2"></i>Asset Details - ${asset.assetnum || 'Unknown'}`;
        }
    }

    async navigateDetailAsset(direction) {
        if (!this.mobileAssets || this.currentDetailIndex < 0) return;

        const newIndex = this.currentDetailIndex + direction;
        if (newIndex < 0 || newIndex >= this.mobileAssets.length) return;

        const newAsset = this.mobileAssets[newIndex];

        // Get current active tab to maintain selection
        const activeTab = document.querySelector('.diary-tab.active');
        const activeTabId = activeTab ? activeTab.id : null;

        // Show loading state with smooth transition
        this.showAssetNavigationLoading();

        try {
            // Load new asset details
            const response = await fetch(`/api/asset/details/${newAsset.assetnum}?siteid=${newAsset.siteid}`);
            if (!response.ok) throw new Error(`HTTP ${response.status}`);

            const result = await response.json();
            if (result.success && result.asset) {
                // Update the modal with smooth transition
                await this.updateAssetDetailWithTransition(result.asset, newIndex, activeTabId);
            }
        } catch (error) {
            console.error('Error navigating to asset:', error);
            this.showError('Failed to load asset details');
            this.hideAssetNavigationLoading();
        }
    }

    showAssetNavigationLoading() {
        const modalBody = document.getElementById('assetDetailModalBody');
        if (modalBody) {
            modalBody.classList.add('asset-transition-loading');

            // Disable navigation buttons
            const navButtons = document.querySelectorAll('.modal-nav-btn');
            navButtons.forEach(btn => btn.disabled = true);
        }
    }

    hideAssetNavigationLoading() {
        const modalBody = document.getElementById('assetDetailModalBody');
        if (modalBody) {
            modalBody.classList.remove('asset-transition-loading');

            // Re-enable navigation buttons
            const navButtons = document.querySelectorAll('.modal-nav-btn');
            navButtons.forEach(btn => btn.disabled = false);
        }
    }

    async updateAssetDetailWithTransition(asset, newIndex, activeTabId) {
        const modalTitle = document.getElementById('assetDetailModalLabel');
        const modalBody = document.getElementById('assetDetailModalBody');

        // Start fade out transition
        modalBody.classList.add('asset-transition-fade-out');

        // Wait for fade out to complete
        await new Promise(resolve => setTimeout(resolve, 200));

        // Update the modal data
        this.currentDetailAsset = asset;
        this.currentDetailIndex = newIndex;

        // Update title with navigation
        modalTitle.innerHTML = this.createModalTitleWithNavigation(asset);

        // Update content
        const isMobile = window.innerWidth <= 768;
        const mobileView = this.createMobileTabbedView(asset);
        const desktopView = this.createDesktopCardView(asset);

        modalBody.innerHTML = `
            <div class="mobile-view d-md-none">
                ${mobileView}
            </div>
            <div class="desktop-view d-none d-md-block">
                ${desktopView}
            </div>
        `;

        // Start fade in transition
        modalBody.classList.remove('asset-transition-fade-out', 'asset-transition-loading');
        modalBody.classList.add('asset-transition-fade-in');

        // Wait for fade in to complete
        await new Promise(resolve => setTimeout(resolve, 200));

        // Clean up transition classes
        modalBody.classList.remove('asset-transition-fade-in');

        // Restore active tab if it exists
        if (activeTabId && isMobile) {
            setTimeout(() => {
                const newActiveTab = document.getElementById(activeTabId);
                if (newActiveTab) {
                    newActiveTab.click();
                }
            }, 50);
        }

        // Re-enable navigation buttons
        this.hideAssetNavigationLoading();
    }

    createStreamlinedAssetDetailView(asset) {
        // Check if mobile view
        const isMobile = window.innerWidth <= 768;

        if (isMobile) {
            return this.createMobileTabbedView(asset);
        } else {
            return this.createDesktopCardView(asset);
        }
    }

    createMobileTabbedView(asset) {
        // Organize asset data into categories
        const categories = this.organizeAssetData(asset);
        const validCategories = categories.filter(category => category.fields.length > 0);

        if (validCategories.length === 0) {
            return '<div class="text-center text-muted p-4">No asset details available</div>';
        }

        // Define colors for vintage diary style tabs
        const tabColors = [
            { bg: '#e3f2fd', border: '#2196f3', text: '#1976d2' }, // Blue
            { bg: '#e8f5e8', border: '#4caf50', text: '#388e3c' }, // Green
            { bg: '#fff3e0', border: '#ff9800', text: '#f57c00' }, // Orange
            { bg: '#fce4ec', border: '#e91e63', text: '#c2185b' }, // Pink
            { bg: '#f3e5f5', border: '#9c27b0', text: '#7b1fa2' }  // Purple
        ];

        // Generate vertical tab navigation (diary style)
        const tabNavHtml = validCategories.map((category, index) => {
            const color = tabColors[index % tabColors.length];
            return `
                <button class="diary-tab ${index === 0 ? 'active' : ''}"
                        id="tab-${category.id}"
                        data-bs-toggle="tab"
                        data-bs-target="#content-${category.id}"
                        type="button"
                        role="tab"
                        style="--tab-bg: ${color.bg}; --tab-border: ${color.border}; --tab-text: ${color.text};">
                    <div class="diary-tab-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <div class="diary-tab-label">${this.getShortTabName(category.title)}</div>
                </button>
            `;
        }).join('');

        // Generate tab content
        const tabContentHtml = validCategories.map((category, index) => `
            <div class="tab-pane fade ${index === 0 ? 'show active' : ''}"
                 id="content-${category.id}"
                 role="tabpanel">
                ${this.createMobileTabContent(category)}
            </div>
        `).join('');

        return `
            <div class="mobile-diary-view">
                <div class="diary-sidebar">
                    <div class="diary-tabs" role="tablist">
                        ${tabNavHtml}
                    </div>
                </div>
                <div class="diary-content">
                    <div class="tab-content">
                        ${tabContentHtml}
                    </div>
                </div>
            </div>
        `;
    }

    getShortTabName(title) {
        const shortNames = {
            'Basic Information': 'Basic',
            'Location & Organization': 'Location',
            'Technical Details': 'Technical',
            'Financial Information': 'Financial',
            'Dates & Status': 'Dates'
        };
        return shortNames[title] || title;
    }

    createDesktopCardView(asset) {
        // Organize asset data into categories
        const categories = this.organizeAssetData(asset);

        // Generate cards for each category
        let cardsHtml = '';

        categories.forEach(category => {
            if (category.fields.length > 0) {
                cardsHtml += this.createAssetDetailCard(category);
            }
        });

        return `<div class="asset-detail-grid">${cardsHtml}</div>`;
    }

    createMobileTabContent(category) {
        const fieldsHtml = category.fields.map(field => `
            <div class="mobile-detail-field">
                <div class="mobile-field-icon">
                    <i class="${field.icon}"></i>
                </div>
                <div class="mobile-field-content">
                    <div class="mobile-field-label">${field.label}</div>
                    <div class="mobile-field-value">
                        ${field.isStatus ?
                            `<span class="badge ${this.getStatusBadgeClass(field.value)}">${field.value}</span>` :
                            field.value || 'N/A'
                        }
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="mobile-tab-panel">
                <div class="mobile-tab-header">
                    <div class="mobile-tab-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <h6 class="mobile-tab-title">${category.title}</h6>
                </div>
                <div class="mobile-detail-fields">
                    ${fieldsHtml}
                </div>
            </div>
        `;
    }

    organizeAssetData(asset) {
        return [
            {
                id: 'basic_information',
                title: 'Basic Information',
                icon: 'fas fa-info-circle',
                fields: [
                    { label: 'Asset Number', value: asset.assetnum, icon: 'fas fa-hashtag' },
                    { label: 'Site ID', value: asset.siteid, icon: 'fas fa-map-marker-alt' },
                    { label: 'Description', value: asset.description, icon: 'fas fa-file-text' },
                    { label: 'Status', value: asset.status, icon: 'fas fa-circle', isStatus: true },
                    { label: 'Asset Tag', value: asset.assettag, icon: 'fas fa-tag' },
                    { label: 'Serial Number', value: asset.serialnum, icon: 'fas fa-barcode' }
                ].filter(field => field.value)
            },
            {
                id: 'location_information',
                title: 'Location & Organization',
                icon: 'fas fa-map-marker-alt',
                fields: [
                    { label: 'Location', value: asset.location, icon: 'fas fa-building' },
                    { label: 'Organization', value: asset.orgid, icon: 'fas fa-sitemap' },
                    { label: 'Parent Asset', value: asset.parent, icon: 'fas fa-link' }
                ].filter(field => field.value)
            },
            {
                id: 'technical_information',
                title: 'Technical Details',
                icon: 'fas fa-cog',
                fields: [
                    { label: 'Model', value: asset.model, icon: 'fas fa-cube' },
                    { label: 'Type', value: asset.type, icon: 'fas fa-shapes' },
                    { label: 'Asset Type', value: asset.assettype, icon: 'fas fa-layer-group' },
                    { label: 'Is Running', value: asset.isrunning ? 'Yes' : 'No', icon: 'fas fa-power-off' },
                    { label: 'Disabled', value: asset.disabled ? 'Yes' : 'No', icon: 'fas fa-ban' }
                ].filter(field => field.value && field.value !== 'No')
            },
            {
                id: 'financial_information',
                title: 'Financial Information',
                icon: 'fas fa-dollar-sign',
                fields: [
                    { label: 'Purchase Price', value: this.formatCurrency(asset.purchaseprice), icon: 'fas fa-shopping-cart' },
                    { label: 'Replace Cost', value: this.formatCurrency(asset.replacecost), icon: 'fas fa-exchange-alt' },
                    { label: 'Total Cost', value: this.formatCurrency(asset.totalcost), icon: 'fas fa-calculator' },
                    { label: 'Vendor', value: asset.vendor, icon: 'fas fa-store' },
                    { label: 'Manufacturer', value: asset.manufacturer, icon: 'fas fa-industry' }
                ].filter(field => field.value)
            },
            {
                id: 'dates_and_status',
                title: 'Dates & Status',
                icon: 'fas fa-calendar-alt',
                fields: [
                    { label: 'Install Date', value: this.formatDate(asset.installdate), icon: 'fas fa-calendar-plus' },
                    { label: 'Change Date', value: this.formatDate(asset.changedate), icon: 'fas fa-calendar-edit' },
                    { label: 'Status Date', value: this.formatDate(asset.statusdate), icon: 'fas fa-calendar-check' },
                    { label: 'Changed By', value: asset.changeby, icon: 'fas fa-user-edit' }
                ].filter(field => field.value)
            }
        ];
    }

    createAssetDetailCard(category) {
        const fieldsHtml = category.fields.map(field => `
            <div class="asset-detail-field">
                <div class="asset-detail-field-icon">
                    <i class="${field.icon}"></i>
                </div>
                <div class="asset-detail-field-content">
                    <div class="asset-detail-field-label">${field.label}</div>
                    <div class="asset-detail-field-value">
                        ${field.isStatus ?
                            `<span class="badge ${this.getStatusBadgeClass(field.value)}">${field.value}</span>` :
                            field.value || 'N/A'
                        }
                    </div>
                </div>
            </div>
        `).join('');

        return `
            <div class="asset-detail-card" data-category="${category.id}">
                <div class="asset-detail-card-header">
                    <div class="asset-detail-card-icon">
                        <i class="${category.icon}"></i>
                    </div>
                    <h6 class="asset-detail-card-title">${category.title}</h6>
                </div>
                <div class="asset-detail-fields">
                    ${fieldsHtml}
                </div>
            </div>
        `;
    }

    async showAssetActions(assetnum, siteid) {
        try {
            console.log(`🔧 Loading asset actions for ${assetnum} in site ${siteid}`);

            const response = await fetch(`/api/asset/actions/${assetnum}?siteid=${siteid}`);

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.success) {
                this.displayAssetActionsModal(assetnum, siteid, result.actions);
            } else {
                this.showError(result.error || 'Failed to load asset actions');
            }

        } catch (error) {
            console.error('Error loading asset actions:', error);
            this.showError('Failed to load asset actions');
        }
    }

    displayAssetActionsModal(assetnum, siteid, actions) {
        // Skip modal - directly execute the view related records action
        console.log(`🔧 Directly executing view related records for ${assetnum}`);
        showRelatedRecordsModal(assetnum, siteid);
    }

    async exportAssetResults() {
        try {
            // Get current search parameters
            const params = new URLSearchParams({
                q: this.currentSearchTerm,
                siteid: this.currentSiteId,
                status: this.currentStatusFilter,
                type: this.currentTypeFilter,
                export: 'true'
            });

            // Create download link
            const downloadUrl = `/api/asset/export?${params}`;
            const link = document.createElement('a');
            link.href = downloadUrl;
            link.download = `assets_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

        } catch (error) {
            console.error('Error exporting assets:', error);
            this.showError('Failed to export assets');
        }
    }

    // Helper methods for asset details formatting
    formatCurrency(value) {
        if (!value || isNaN(value)) return null;
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'USD'
        }).format(value);
    }

    formatDate(dateString) {
        if (!dateString) return null;
        try {
            const date = new Date(dateString);
            return date.toLocaleDateString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric'
            });
        } catch (error) {
            return dateString; // Return original if parsing fails
        }
    }

    getStatusBadgeClass(status) {
        if (!status) return 'bg-secondary';

        const statusLower = status.toLowerCase();

        if (statusLower.includes('active') || statusLower.includes('operating')) {
            return 'bg-success';
        } else if (statusLower.includes('inactive') || statusLower.includes('decommissioned')) {
            return 'bg-danger';
        } else if (statusLower.includes('maintenance') || statusLower.includes('repair')) {
            return 'bg-warning';
        } else if (statusLower.includes('planned') || statusLower.includes('pending')) {
            return 'bg-info';
        } else {
            return 'bg-secondary';
        }
    }

    // Create operation methods
    async showCreateWorkOrderModal(asset) {
        const modalBody = document.getElementById('createWorkOrderModalBody');
        modalBody.innerHTML = this.createWorkOrderForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('createWorkOrderModal'));
        modal.show();

        // Populate the Created By field with current user's personid
        try {
            const response = await fetch('/api/current-user');
            const userInfo = await response.json();
            if (userInfo.success) {
                const createdByField = document.getElementById('woCreatedBy');
                if (createdByField) {
                    createdByField.value = userInfo.personid || userInfo.username;
                    createdByField.placeholder = `${userInfo.displayname || userInfo.username} (${userInfo.personid || userInfo.username})`;
                }
            }
        } catch (error) {
            console.warn('Could not fetch user info for Created By field:', error);
        }

        // Add form submission handler
        const form = document.getElementById('createWorkOrderForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitCreateWorkOrder(asset);
            });
        }
    }

    async showCreateServiceRequestModal(asset) {
        const modalBody = document.getElementById('createServiceRequestModalBody');
        modalBody.innerHTML = this.createServiceRequestForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('createServiceRequestModal'));
        modal.show();

        // Populate the Requested By field with current user's personid
        try {
            const response = await fetch('/api/current-user');
            const userInfo = await response.json();
            if (userInfo.success) {
                const requestedByField = document.getElementById('srRequestedBy');
                if (requestedByField) {
                    requestedByField.value = userInfo.personid || userInfo.username;
                    requestedByField.placeholder = `${userInfo.displayname || userInfo.username} (${userInfo.personid || userInfo.username})`;
                }
            }
        } catch (error) {
            console.warn('Could not fetch user info for Requested By field:', error);
        }

        // Add form submission handler
        const form = document.getElementById('createServiceRequestForm');
        if (form) {
            form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitCreateServiceRequest(asset);
            });
        }
    }

    showCreateIncidentModal(asset) {
        const modalBody = document.getElementById('createIncidentModalBody');
        modalBody.innerHTML = this.createIncidentForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('createIncidentModal'));
        modal.show();
    }

    showCreateProblemModal(asset) {
        const modalBody = document.getElementById('createProblemModalBody');
        modalBody.innerHTML = this.createProblemForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('createProblemModal'));
        modal.show();
    }

    showCreateChangeModal(asset) {
        const modalBody = document.getElementById('createChangeModalBody');
        modalBody.innerHTML = this.createChangeForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('createChangeModal'));
        modal.show();
    }

    showCreateReleaseModal(asset) {
        const modalBody = document.getElementById('createReleaseModalBody');
        modalBody.innerHTML = this.createReleaseForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('createReleaseModal'));
        modal.show();
    }

    showCreateInspectionModal(asset) {
        const modalBody = document.getElementById('createInspectionModalBody');
        modalBody.innerHTML = this.createInspectionForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('createInspectionModal'));
        modal.show();
    }

    showReportDowntimeModal(asset) {
        const modalBody = document.getElementById('reportDowntimeModalBody');
        modalBody.innerHTML = this.createDowntimeForm(asset);

        const modal = new bootstrap.Modal(document.getElementById('reportDowntimeModal'));
        modal.show();
    }

    // Form creation methods

    createWorkOrderForm(asset) {
        return `
            <form id="createWorkOrderForm">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Creating work order for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>

                <!-- Asset Context Information (Readonly) -->
                <div class="card mb-3 bg-light">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Asset Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="woAssetNum" class="form-label">Asset Number</label>
                                    <input type="text" class="form-control" id="woAssetNum" value="${asset.assetnum}" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="woSiteId" class="form-label">Site ID</label>
                                    <input type="text" class="form-control" id="woSiteId" value="${asset.siteid}" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="woOrgId" class="form-label">Organization</label>
                                    <input type="text" class="form-control" id="woOrgId" value="${asset.orgid || 'USARMY'}" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-0">
                                    <label class="form-label">Asset Description</label>
                                    <div class="form-control-plaintext">${asset.description || 'No description available'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Work Order Details -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="woDescription" class="form-label">Work Order Description *</label>
                            <input type="text" class="form-control" id="woDescription" required placeholder="Brief description of the work to be performed">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woWorkType" class="form-label">Work Type *</label>
                            <select class="form-select" id="woWorkType" required>
                                <option value="CM" selected>CM - Corrective Maintenance</option>
                                <option value="PM">PM - Preventive Maintenance</option>
                                <option value="EM">EM - Emergency</option>
                                <option value="CAP">CAP - Capital Project</option>
                                <option value="SAFETY">SAFETY - Safety Work</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woPriority" class="form-label">Priority *</label>
                            <select class="form-select" id="woPriority" required>
                                <option value="1">1 - Emergency</option>
                                <option value="2">2 - High</option>
                                <option value="3" selected>3 - Medium</option>
                                <option value="4">4 - Low</option>
                                <option value="5">5 - Planning</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woTargetStartDate" class="form-label">Target Start Date</label>
                            <input type="datetime-local" class="form-control" id="woTargetStartDate">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="woCreatedBy" class="form-label">Created By</label>
                            <input type="text" class="form-control" id="woCreatedBy" placeholder="Your name or ID">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="woLongDescription" class="form-label">Detailed Description</label>
                    <textarea class="form-control" id="woLongDescription" rows="4" placeholder="Provide detailed information about the work to be performed, including any specific requirements, safety considerations, or special instructions..."></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-wrench me-1"></i>Create Work Order
                    </button>
                </div>
            </form>
        `;
    }

    createServiceRequestForm(asset) {
        return `
            <form id="createServiceRequestForm">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Creating service request for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>

                <!-- Asset Context Information (Readonly) -->
                <div class="card mb-3 bg-light">
                    <div class="card-header">
                        <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Asset Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="srAssetNum" class="form-label">Asset Number</label>
                                    <input type="text" class="form-control" id="srAssetNum" value="${asset.assetnum}" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="srSiteId" class="form-label">Site ID</label>
                                    <input type="text" class="form-control" id="srSiteId" value="${asset.siteid}" readonly>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label for="srOrgId" class="form-label">Organization</label>
                                    <input type="text" class="form-control" id="srOrgId" value="${asset.orgid || 'USARMY'}" readonly>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-12">
                                <div class="mb-0">
                                    <label class="form-label">Asset Description</label>
                                    <div class="form-control-plaintext">${asset.description || 'No description available'}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service Request Details -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="srDescription" class="form-label">Service Request Description *</label>
                            <input type="text" class="form-control" id="srDescription" required placeholder="Brief description of the service needed">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="srPriority" class="form-label">Priority *</label>
                            <select class="form-select" id="srPriority" required>
                                <option value="1">1 - Emergency</option>
                                <option value="2">2 - High</option>
                                <option value="3" selected>3 - Medium</option>
                                <option value="4">4 - Low</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="srRequestedBy" class="form-label">Requested By</label>
                            <input type="text" class="form-control" id="srRequestedBy" placeholder="Your name or ID">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="srRequestedDate" class="form-label">Requested Date</label>
                            <input type="datetime-local" class="form-control" id="srRequestedDate">
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="srOwnerGroup" class="form-label">Owner Group</label>
                            <select class="form-select" id="srOwnerGroup">
                                <option value="MAINT" selected>MAINT - Maintenance</option>
                                <option value="IT">IT - Information Technology</option>
                                <option value="FACILITIES">FACILITIES - Facilities Management</option>
                                <option value="SECURITY">SECURITY - Security</option>
                                <option value="SAFETY">SAFETY - Safety</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="srLongDescription" class="form-label">Detailed Description</label>
                    <textarea class="form-control" id="srLongDescription" rows="4" placeholder="Provide detailed information about the service request, including any specific requirements or urgency..."></textarea>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-info">
                        <i class="fas fa-headset me-1"></i>Create Service Request
                    </button>
                </div>
            </form>
        `;
    }

    createIncidentForm(asset) {
        return `
            <form id="createIncidentForm">
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Creating incident for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="incidentDescription" class="form-label">Incident Description *</label>
                            <input type="text" class="form-control" id="incidentDescription" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="incidentPriority" class="form-label">Priority</label>
                            <select class="form-select" id="incidentPriority">
                                <option value="1" selected>1 - Critical</option>
                                <option value="2">2 - High</option>
                                <option value="3">3 - Medium</option>
                                <option value="4">4 - Low</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="incidentReportedBy" class="form-label">Reported By</label>
                            <input type="text" class="form-control" id="incidentReportedBy">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="incidentDate" class="form-label">Incident Date</label>
                            <input type="datetime-local" class="form-control" id="incidentDate">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="incidentDetails" class="form-label">Incident Details</label>
                    <textarea class="form-control" id="incidentDetails" rows="4" placeholder="Describe the incident in detail..."></textarea>
                </div>
                <input type="hidden" id="incidentAssetNum" value="${asset.assetnum}">
                <input type="hidden" id="incidentSiteId" value="${asset.siteid}">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-exclamation-triangle me-1"></i>Create Incident
                    </button>
                </div>
            </form>
        `;
    }

    createProblemForm(asset) {
        return `
            <form id="createProblemForm">
                <div class="alert alert-warning">
                    <i class="fas fa-bug me-2"></i>
                    Creating problem record for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="problemDescription" class="form-label">Problem Description *</label>
                            <input type="text" class="form-control" id="problemDescription" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="problemPriority" class="form-label">Priority</label>
                            <select class="form-select" id="problemPriority">
                                <option value="1">1 - Critical</option>
                                <option value="2" selected>2 - High</option>
                                <option value="3">3 - Medium</option>
                                <option value="4">4 - Low</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="problemCategory" class="form-label">Problem Category</label>
                            <select class="form-select" id="problemCategory">
                                <option value="HARDWARE">Hardware</option>
                                <option value="SOFTWARE">Software</option>
                                <option value="NETWORK">Network</option>
                                <option value="PERFORMANCE">Performance</option>
                                <option value="OTHER">Other</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="problemReportedBy" class="form-label">Reported By</label>
                            <input type="text" class="form-control" id="problemReportedBy">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="problemDetails" class="form-label">Problem Details</label>
                    <textarea class="form-control" id="problemDetails" rows="4" placeholder="Describe the problem in detail..."></textarea>
                </div>
                <input type="hidden" id="problemAssetNum" value="${asset.assetnum}">
                <input type="hidden" id="problemSiteId" value="${asset.siteid}">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-bug me-1"></i>Create Problem
                    </button>
                </div>
            </form>
        `;
    }

    createChangeForm(asset) {
        return `
            <form id="createChangeForm">
                <div class="alert alert-primary">
                    <i class="fas fa-exchange-alt me-2"></i>
                    Creating change record for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="changeDescription" class="form-label">Change Description *</label>
                            <input type="text" class="form-control" id="changeDescription" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="changeType" class="form-label">Change Type</label>
                            <select class="form-select" id="changeType">
                                <option value="NORMAL">Normal</option>
                                <option value="STANDARD">Standard</option>
                                <option value="EMERGENCY">Emergency</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="changeRequestedBy" class="form-label">Requested By</label>
                            <input type="text" class="form-control" id="changeRequestedBy">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="changeTargetDate" class="form-label">Target Implementation Date</label>
                            <input type="datetime-local" class="form-control" id="changeTargetDate">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="changeJustification" class="form-label">Change Justification</label>
                    <textarea class="form-control" id="changeJustification" rows="3" placeholder="Explain why this change is needed..."></textarea>
                </div>
                <input type="hidden" id="changeAssetNum" value="${asset.assetnum}">
                <input type="hidden" id="changeSiteId" value="${asset.siteid}">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-exchange-alt me-1"></i>Create Change
                    </button>
                </div>
            </form>
        `;
    }

    createReleaseForm(asset) {
        return `
            <form id="createReleaseForm">
                <div class="alert alert-success">
                    <i class="fas fa-rocket me-2"></i>
                    Creating release record for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="releaseDescription" class="form-label">Release Description *</label>
                            <input type="text" class="form-control" id="releaseDescription" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="releaseVersion" class="form-label">Version</label>
                            <input type="text" class="form-control" id="releaseVersion" placeholder="e.g., 1.0.0">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="releaseManager" class="form-label">Release Manager</label>
                            <input type="text" class="form-control" id="releaseManager">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="releaseDate" class="form-label">Planned Release Date</label>
                            <input type="datetime-local" class="form-control" id="releaseDate">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="releaseNotes" class="form-label">Release Notes</label>
                    <textarea class="form-control" id="releaseNotes" rows="4" placeholder="Describe what's included in this release..."></textarea>
                </div>
                <input type="hidden" id="releaseAssetNum" value="${asset.assetnum}">
                <input type="hidden" id="releaseSiteId" value="${asset.siteid}">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-rocket me-1"></i>Create Release
                    </button>
                </div>
            </form>
        `;
    }

    createInspectionForm(asset) {
        return `
            <form id="createInspectionForm">
                <div class="alert alert-secondary">
                    <i class="fas fa-clipboard-check me-2"></i>
                    Creating unscheduled inspection for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="inspectionDescription" class="form-label">Inspection Description *</label>
                            <input type="text" class="form-control" id="inspectionDescription" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="inspectionType" class="form-label">Inspection Type</label>
                            <select class="form-select" id="inspectionType">
                                <option value="SAFETY">Safety</option>
                                <option value="QUALITY">Quality</option>
                                <option value="COMPLIANCE">Compliance</option>
                                <option value="CONDITION">Condition</option>
                                <option value="PERFORMANCE">Performance</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="inspectionRequestedBy" class="form-label">Requested By</label>
                            <input type="text" class="form-control" id="inspectionRequestedBy">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="inspectionTargetDate" class="form-label">Target Inspection Date</label>
                            <input type="datetime-local" class="form-control" id="inspectionTargetDate">
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="inspectionReason" class="form-label">Reason for Inspection</label>
                    <textarea class="form-control" id="inspectionReason" rows="3" placeholder="Explain why this inspection is needed..."></textarea>
                </div>
                <input type="hidden" id="inspectionAssetNum" value="${asset.assetnum}">
                <input type="hidden" id="inspectionSiteId" value="${asset.siteid}">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-secondary">
                        <i class="fas fa-clipboard-check me-1"></i>Create Inspection
                    </button>
                </div>
            </form>
        `;
    }

    createDowntimeForm(asset) {
        return `
            <form id="createDowntimeForm">
                <div class="alert alert-danger">
                    <i class="fas fa-clock me-2"></i>
                    Reporting downtime for asset: <strong>${asset.assetnum}</strong> (${asset.siteid})
                </div>
                <div class="row">
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="downtimeDescription" class="form-label">Downtime Description *</label>
                            <input type="text" class="form-control" id="downtimeDescription" required>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="downtimeCategory" class="form-label">Downtime Category</label>
                            <select class="form-select" id="downtimeCategory">
                                <option value="PLANNED">Planned</option>
                                <option value="UNPLANNED" selected>Unplanned</option>
                                <option value="EMERGENCY">Emergency</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="downtimeStartDate" class="form-label">Downtime Start *</label>
                            <input type="datetime-local" class="form-control" id="downtimeStartDate" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="downtimeEndDate" class="form-label">Downtime End (if known)</label>
                            <input type="datetime-local" class="form-control" id="downtimeEndDate">
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="downtimeReportedBy" class="form-label">Reported By</label>
                            <input type="text" class="form-control" id="downtimeReportedBy">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="downtimeImpact" class="form-label">Impact Level</label>
                            <select class="form-select" id="downtimeImpact">
                                <option value="LOW">Low</option>
                                <option value="MEDIUM">Medium</option>
                                <option value="HIGH" selected>High</option>
                                <option value="CRITICAL">Critical</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <label for="downtimeReason" class="form-label">Reason for Downtime</label>
                    <textarea class="form-control" id="downtimeReason" rows="3" placeholder="Describe the cause of the downtime..."></textarea>
                </div>
                <input type="hidden" id="downtimeAssetNum" value="${asset.assetnum}">
                <input type="hidden" id="downtimeSiteId" value="${asset.siteid}">
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-clock me-1"></i>Report Downtime
                    </button>
                </div>
            </form>
        `;
    }

    // Form submission methods
    async submitCreateWorkOrder(asset) {
        try {
            // Get form data
            const formData = {
                assetnum: document.getElementById('woAssetNum').value,
                siteid: document.getElementById('woSiteId').value,
                orgid: document.getElementById('woOrgId').value,
                description: document.getElementById('woDescription').value,
                worktype: document.getElementById('woWorkType').value,
                priority: document.getElementById('woPriority').value,
                longdescription: document.getElementById('woLongDescription').value,
                targstartdate: document.getElementById('woTargetStartDate').value,
                createdby: document.getElementById('woCreatedBy').value
            };

            // Validate required fields
            if (!formData.description) {
                this.showAlert('Description is required', 'error');
                return;
            }

            // Show loading state for AI analysis
            const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-brain fa-spin me-1"></i>AI Analysis...';
            submitBtn.disabled = true;

            try {
                // Step 1: Perform AI Analysis
                const analysisResult = await this.performAIAnalysis(asset, formData);

                if (analysisResult.success) {
                    // Close the work order creation modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createWorkOrderModal'));
                    modal.hide();

                    // Show AI insights modal
                    this.showAIInsightsModal(analysisResult, asset, formData);
                } else {
                    // If AI analysis fails, proceed with normal creation
                    console.warn('AI analysis failed, proceeding with normal creation:', analysisResult.error);
                    this.showAlert('AI analysis unavailable. Proceeding with normal creation.', 'warning');

                    // Change button text to normal creation
                    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

                    // Proceed with direct creation
                    await this.createWorkOrderWithoutAI(formData);

                    // Close modal
                    const modal = bootstrap.Modal.getInstance(document.getElementById('createWorkOrderModal'));
                    modal.hide();
                }
            } catch (analysisError) {
                console.error('AI analysis error:', analysisError);
                this.showAlert('AI analysis failed. Proceeding with normal creation.', 'warning');

                // Change button text to normal creation
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';

                // Proceed with direct creation
                await this.createWorkOrderWithoutAI(formData);

                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('createWorkOrderModal'));
                modal.hide();
            }

        } catch (error) {
            console.error('Error creating work order:', error);
            this.showAlert('Error creating work order: ' + error.message, 'error');
        } finally {
            // Reset button state
            const submitBtn = document.querySelector('#createWorkOrderForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-wrench me-1"></i>Create Work Order';
                submitBtn.disabled = false;
            }
        }
    }

    async performAIAnalysis(asset, formData) {
        try {
            console.log('🤖 Starting AI analysis for work order creation...');

            const response = await fetch('/api/ai-analysis/analyze-workorder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    asset_data: {
                        assetnum: asset.assetnum,
                        siteid: asset.siteid,
                        description: asset.description || ''
                    },
                    user_description: formData.description + (formData.longdescription ? ' ' + formData.longdescription : ''),
                    form_data: formData
                })
            });

            if (!response.ok) {
                throw new Error(`AI analysis request failed: ${response.status}`);
            }

            const result = await response.json();
            console.log('🤖 AI analysis completed:', result);

            return result;

        } catch (error) {
            console.error('AI analysis error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async submitCreateServiceRequest(asset) {
        try {
            // Get form data
            const formData = {
                assetnum: document.getElementById('srAssetNum').value,
                siteid: document.getElementById('srSiteId').value,
                orgid: document.getElementById('srOrgId').value,
                description: document.getElementById('srDescription').value,
                priority: document.getElementById('srPriority').value,
                longdescription: document.getElementById('srLongDescription').value,
                requestedby: document.getElementById('srRequestedBy').value,
                requesteddate: document.getElementById('srRequestedDate').value,
                ownergroup: document.getElementById('srOwnerGroup').value
            };

            // Validate required fields
            if (!formData.description) {
                this.showAlert('Description is required', 'error');
                return;
            }

            // Show loading state
            const submitBtn = document.querySelector('#createServiceRequestForm button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Creating...';
            submitBtn.disabled = true;

            // Submit to API
            const response = await fetch('/api/asset/create-servicerequest', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            const result = await response.json();

            if (result.success) {
                // Close modal
                const modal = bootstrap.Modal.getInstance(document.getElementById('createServiceRequestModal'));
                modal.hide();

                // Show success response card
                this.showServiceRequestCreationSuccess(result);
            } else {
                // Handle specific error types
                const errorMessage = result.error || 'Failed to create service request';

                if (errorMessage.includes('Authentication session has expired')) {
                    this.showAuthenticationError('service request creation');
                } else if (errorMessage.includes('Invalid asset number')) {
                    this.showAlert('Invalid asset: The selected asset is not valid or not in operating status.', 'error');
                } else if (errorMessage.includes('Access denied')) {
                    this.showAlert('Permission denied: You do not have permission to create service requests.', 'error');
                } else {
                    this.showAlert(errorMessage, 'error');
                }
            }

        } catch (error) {
            console.error('Error creating service request:', error);
            this.showAlert('Error creating service request: ' + error.message, 'error');
        } finally {
            // Reset button state
            const submitBtn = document.querySelector('#createServiceRequestForm button[type="submit"]');
            if (submitBtn) {
                submitBtn.innerHTML = '<i class="fas fa-headset me-1"></i>Create Service Request';
                submitBtn.disabled = false;
            }
        }
    }

    // Response card methods
    showWorkOrderCreationSuccess(result) {
        const data = result.data || {};
        const wonum = result.wonum || data.wonum || data.workorder_number || 'Generated by Maximo';
        const assetnum = result.assetnum || data.assetnum || 'N/A';
        const siteid = result.siteid || data.siteid || 'N/A';

        // Determine if we have a real work order number or just a placeholder
        const hasRealWonum = wonum && wonum !== 'Generated by Maximo';

        const responseCard = `
            <div class="alert alert-success alert-dismissible fade show shadow-lg border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 450px;">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-success bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-wrench text-success" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="alert-heading mb-1 text-success">
                            <i class="fas fa-check-circle me-2"></i>Work Order Created!
                        </h5>
                        <p class="mb-0 text-muted">Successfully created in Maximo</p>
                    </div>
                </div>

                <div class="bg-light rounded p-3 mb-3">
                    <div class="row g-2">
                        <div class="col-12">
                            <strong class="text-primary">Work Order Number:</strong>
                            <div class="fs-4 fw-bold text-primary d-flex align-items-center">
                                ${wonum}
                                ${hasRealWonum ? '<i class="fas fa-check-circle text-success ms-2" title="Work order number confirmed"></i>' : '<i class="fas fa-clock text-warning ms-2" title="Work order created, number pending"></i>'}
                            </div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Asset:</small>
                            <div class="fw-semibold">${assetnum}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Site:</small>
                            <div class="fw-semibold">${siteid}</div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Status:</small>
                            <span class="badge bg-warning text-dark">WAPPR - Waiting for Approval</span>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2 flex-wrap">
                    ${hasRealWonum ?
                        `<button type="button" class="btn btn-sm btn-primary" onclick="window.open('/workorder/${wonum}', '_blank')">
                            <i class="fas fa-eye me-1"></i>View Details
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="window.openWorkOrderView('${wonum}', '${siteid}')">
                            <i class="fas fa-search me-1"></i>Find in List
                        </button>` :
                        `<button type="button" class="btn btn-sm btn-outline-info" onclick="window.open('/enhanced-workorders', '_blank')">
                            <i class="fas fa-list me-1"></i>View Work Orders
                        </button>`
                    }
                    <button type="button" class="btn btn-sm btn-outline-secondary ms-auto" data-bs-dismiss="alert">
                        <i class="fas fa-times"></i>
                    </button>
                </div>

                <script>
                    // Define the function in global scope for the onclick handler
                    window.openWorkOrderView = function(wonum, siteid) {
                        // Open the enhanced work orders page with search parameters
                        const searchParams = new URLSearchParams({
                            wonum: wonum,
                            siteid: siteid,
                            auto_search: 'true'
                        });
                        window.open('/enhanced-workorders?' + searchParams.toString(), '_blank');
                    };
                </script>
            </div>
        `;

        // Insert the response card
        document.body.insertAdjacentHTML('beforeend', responseCard);

        // Auto-dismiss after 15 seconds (longer for important success messages)
        setTimeout(() => {
            const alertElement = document.querySelector('.alert:last-of-type');
            if (alertElement) {
                const alert = new bootstrap.Alert(alertElement);
                alert.close();
            }
        }, 15000);
    }

    showServiceRequestCreationSuccess(result) {
        const data = result.data || {};
        const srnum = data.srnum || data.ticketid || 'Generated by Maximo';

        const responseCard = `
            <div class="alert alert-info alert-dismissible fade show shadow-lg border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-info bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-headset text-info" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="alert-heading mb-1 text-info">
                            <i class="fas fa-check-circle me-2"></i>Service Request Created!
                        </h5>
                        <p class="mb-0 text-muted">Successfully created in Maximo</p>
                    </div>
                </div>

                <div class="bg-light rounded p-3 mb-3">
                    <div class="row g-2">
                        <div class="col-12">
                            <strong class="text-primary">Service Request Number:</strong>
                            <div class="fs-4 fw-bold text-primary">${srnum}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Asset:</small>
                            <div class="fw-semibold">${result.assetnum || 'N/A'}</div>
                        </div>
                        <div class="col-6">
                            <small class="text-muted">Site:</small>
                            <div class="fw-semibold">${result.siteid || 'N/A'}</div>
                        </div>
                        <div class="col-12">
                            <small class="text-muted">Status:</small>
                            <span class="badge bg-success">NEW</span>
                        </div>
                    </div>
                </div>

                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-outline-info" onclick="alert('Service Request details view not yet implemented')">
                        <i class="fas fa-external-link-alt me-1"></i>View Details
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary ms-auto" data-bs-dismiss="alert">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        // Insert the response card
        document.body.insertAdjacentHTML('beforeend', responseCard);

        // Auto-dismiss after 10 seconds
        setTimeout(() => {
            const alertElement = document.querySelector('.alert:last-of-type');
            if (alertElement) {
                const alert = new bootstrap.Alert(alertElement);
                alert.close();
            }
        }, 10000);
    }

    showAlert(message, type = 'info') {
        const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
        const iconClass = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 400px;">
                <i class="${iconClass} me-2"></i>
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            const alertElement = document.querySelector('.alert:last-of-type');
            if (alertElement) {
                const alert = new bootstrap.Alert(alertElement);
                alert.close();
            }
        }, 5000);
    }

    showAuthenticationError(operation) {
        const alertHtml = `
            <div class="alert alert-warning alert-dismissible fade show shadow-lg border-0" role="alert" style="position: fixed; top: 20px; right: 20px; z-index: 9999; max-width: 450px;">
                <div class="d-flex align-items-center mb-3">
                    <div class="flex-shrink-0">
                        <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                            <i class="fas fa-exclamation-triangle text-warning" style="font-size: 1.5rem;"></i>
                        </div>
                    </div>
                    <div class="flex-grow-1 ms-3">
                        <h5 class="alert-heading mb-1 text-warning">
                            <i class="fas fa-lock me-2"></i>Session Expired
                        </h5>
                        <p class="mb-0 text-muted">Your authentication session has expired</p>
                    </div>
                </div>

                <div class="bg-light rounded p-3 mb-3">
                    <p class="mb-2">
                        <strong>Unable to complete ${operation}</strong><br>
                        Your session has expired and you need to log in again.
                    </p>
                    <small class="text-muted">
                        <i class="fas fa-info-circle me-1"></i>
                        This happens automatically for security reasons.
                    </small>
                </div>

                <div class="d-flex gap-2">
                    <button type="button" class="btn btn-sm btn-warning" onclick="window.location.reload()">
                        <i class="fas fa-refresh me-1"></i>Refresh Page
                    </button>
                    <button type="button" class="btn btn-sm btn-outline-secondary ms-auto" data-bs-dismiss="alert">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', alertHtml);

        // Auto-dismiss after 15 seconds (longer for important auth errors)
        setTimeout(() => {
            const alertElement = document.querySelector('.alert:last-of-type');
            if (alertElement) {
                const alert = new bootstrap.Alert(alertElement);
                alert.close();
            }
        }, 15000);
    }

    // AI Work Order Insights Modal Methods
    showAIInsightsModal(analysisResult, asset, formData) {
        const modalHtml = this.createAIInsightsModalHTML(analysisResult, asset, formData);

        // Remove any existing AI insights modal
        const existingModal = document.getElementById('aiInsightsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('aiInsightsModal'));
        modal.show();

        // Add event listeners
        this.setupAIInsightsEventListeners(analysisResult, asset, formData);
    }

    createAIInsightsModalHTML(analysisResult, asset, formData) {
        const kpis = analysisResult.kpis || {};
        const insights = analysisResult.insights || {};
        const duplicationAnalysis = analysisResult.duplication_analysis || {};
        const recommendations = analysisResult.recommendations || [];
        const existingWorkorders = analysisResult.existing_workorders || [];

        return `
            <div class="modal fade" id="aiInsightsModal" tabindex="-1" aria-labelledby="aiInsightsModalLabel" aria-hidden="true">
                <div class="modal-dialog modal-xl ai-insights-modal">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="aiInsightsModalLabel">
                                <i class="fas fa-brain me-2"></i><span class="d-none d-sm-inline">AI Work Order Insights & </span>Duplication Analysis
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body ai-insights-body">
                            ${this.createAIInsightsContent(analysisResult, asset, formData)}
                        </div>
                        <div class="modal-footer ai-insights-footer">
                            <div class="d-flex justify-content-between w-100 flex-wrap gap-2">
                                <div class="order-2 order-sm-1">
                                    <button type="button" class="btn btn-outline-secondary btn-sm" data-bs-dismiss="modal">
                                        <i class="fas fa-times me-1"></i><span class="d-none d-sm-inline">Cancel Creation</span><span class="d-sm-none">Cancel</span>
                                    </button>
                                </div>
                                <div class="order-1 order-sm-2 d-flex gap-2 flex-wrap">
                                    ${duplicationAnalysis.risk_level === 'HIGH' ?
                                        `<button type="button" class="btn btn-warning btn-sm" id="reviewExistingBtn">
                                            <i class="fas fa-eye me-1"></i><span class="d-none d-sm-inline">Review Existing</span><span class="d-sm-none">Review</span>
                                        </button>` : ''
                                    }
                                    <button type="button" class="btn btn-success btn-sm" id="proceedWithCreationBtn">
                                        <i class="fas fa-check me-1"></i><span class="d-none d-sm-inline">Proceed with Creation</span><span class="d-sm-none">Proceed</span>
                                        ${duplicationAnalysis.risk_level === 'HIGH' ? '<span class="d-none d-md-inline"> (Acknowledge Duplicates)</span>' : ''}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <style>
            /* AI Insights Modal Mobile Optimizations */
            @media (max-width: 768px) {
                .ai-insights-modal .modal-dialog {
                    margin: 0.5rem;
                    max-width: calc(100% - 1rem);
                    height: calc(100vh - 1rem);
                }

                .ai-insights-modal .modal-content {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                }

                .ai-insights-body {
                    flex: 1;
                    overflow-y: auto;
                    padding: 1rem !important;
                    max-height: calc(100vh - 200px);
                }

                .ai-insights-footer {
                    padding: 0.75rem 1rem !important;
                    border-top: 1px solid #dee2e6;
                }

                /* Compact KPI cards on mobile */
                .ai-insights-body .row .col-md-3 {
                    flex: 0 0 50%;
                    max-width: 50%;
                    margin-bottom: 0.5rem;
                }

                .ai-insights-body .card {
                    margin-bottom: 0.75rem !important;
                }

                .ai-insights-body .card-body {
                    padding: 0.75rem !important;
                }

                .ai-insights-body h3 {
                    font-size: 1.5rem !important;
                }

                .ai-insights-body h5 {
                    font-size: 1.1rem !important;
                    margin-bottom: 0.75rem !important;
                }

                .ai-insights-body h6 {
                    font-size: 1rem !important;
                    margin-bottom: 0.5rem !important;
                }

                .ai-insights-body .alert {
                    padding: 0.75rem !important;
                    margin-bottom: 0.75rem !important;
                }

                /* Compact work order cards */
                .ai-insights-body .card .row {
                    margin: 0 !important;
                }

                .ai-insights-body .card .col-md-6 {
                    padding: 0.25rem !important;
                }

                /* Scrollable sections with max height */
                .ai-insights-body > div:nth-child(n+4) {
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid #e9ecef;
                    border-radius: 0.375rem;
                    padding: 0.75rem;
                    margin-bottom: 0.75rem;
                }

                /* Hide less critical information on mobile */
                .ai-insights-body .text-muted {
                    display: none;
                }

                .ai-insights-body small {
                    font-size: 0.75rem !important;
                }
            }

            /* Tablet optimizations */
            @media (min-width: 769px) and (max-width: 1024px) {
                .ai-insights-modal .modal-dialog {
                    max-width: 90%;
                }

                .ai-insights-body {
                    max-height: 70vh;
                    overflow-y: auto;
                }
            }
            </style>
        `;
    }

    createAIInsightsContent(analysisResult, asset, formData) {
        const kpis = analysisResult.kpis || {};
        const insights = analysisResult.insights || {};
        const duplicationAnalysis = analysisResult.duplication_analysis || {};
        const recommendations = analysisResult.recommendations || [];
        const existingWorkorders = analysisResult.existing_workorders || [];

        return `
            <!-- Analysis Summary - Compact for mobile -->
            <div class="row mb-3">
                <div class="col-12">
                    <div class="alert alert-info">
                        <div class="d-flex align-items-center">
                            <i class="fas fa-info-circle me-2 fs-5"></i>
                            <div>
                                <h6 class="mb-1">AI Analysis Complete</h6>
                                <p class="mb-0">Asset <strong>${asset.assetnum}</strong> in <strong>${asset.siteid}</strong></p>
                                <small class="text-muted d-none d-sm-block">Analysis completed in ${analysisResult.analysis_duration_ms}ms</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- KPIs Row - Responsive Grid -->
            <div class="row mb-3">
                <div class="col-6 col-md-3 mb-2">
                    <div class="card text-center">
                        <div class="card-body py-2">
                            <h3 class="text-primary mb-1">${kpis.total_open_workorders || 0}</h3>
                            <p class="card-text small mb-0">Open WOs</p>
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-3 mb-2">
                    <div class="card text-center">
                        <div class="card-body py-2">
                            <h3 class="text-warning mb-1">${kpis.potential_duplicates || 0}</h3>
                            <p class="card-text small mb-0">Duplicates</p>
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-3 mb-2">
                    <div class="card text-center">
                        <div class="card-body py-2">
                            <h3 class="text-info mb-1">${Math.round((kpis.highest_similarity_score || 0) * 100)}%</h3>
                            <p class="card-text small mb-0">Similarity</p>
                        </div>
                    </div>
                </div>
                <div class="col-6 col-md-3 mb-2">
                    <div class="card text-center">
                        <div class="card-body py-2">
                            <h3 class="${this.getRiskLevelColor(kpis.risk_level)} mb-1">${kpis.risk_level || 'NONE'}</h3>
                            <p class="card-text small mb-0">Risk Level</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recommendations -->
            ${recommendations.length > 0 ? this.createRecommendationsSection(recommendations) : ''}

            <!-- Duplication Analysis -->
            ${this.createDuplicationAnalysisSection(duplicationAnalysis)}

            <!-- Collapsible Sections for Mobile -->
            <div class="accordion d-md-none" id="mobileAccordion">
                ${existingWorkorders.length > 0 ? this.createCollapsibleSection('existing', 'Existing Work Orders', this.createExistingWorkOrdersSection(existingWorkorders, true)) : ''}
                ${this.createCollapsibleSection('intended', 'Your Intended Work Order', this.createIntendedWorkOrderSection(formData, true))}
            </div>

            <!-- Desktop Sections -->
            <div class="d-none d-md-block">
                ${existingWorkorders.length > 0 ? this.createExistingWorkOrdersSection(existingWorkorders) : ''}
                ${this.createIntendedWorkOrderSection(formData)}
            </div>
        `;
    }

    getRiskLevelColor(riskLevel) {
        switch(riskLevel) {
            case 'HIGH': return 'text-danger';
            case 'MEDIUM': return 'text-warning';
            case 'LOW': return 'text-success';
            default: return 'text-muted';
        }
    }

    createCollapsibleSection(id, title, content) {
        return `
            <div class="accordion-item">
                <h2 class="accordion-header" id="heading${id}">
                    <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#collapse${id}" aria-expanded="false" aria-controls="collapse${id}">
                        <i class="fas fa-chevron-down me-2"></i>${title}
                    </button>
                </h2>
                <div id="collapse${id}" class="accordion-collapse collapse" aria-labelledby="heading${id}" data-bs-parent="#mobileAccordion">
                    <div class="accordion-body">
                        ${content}
                    </div>
                </div>
            </div>
        `;
    }

    createRecommendationsSection(recommendations) {
        const recommendationCards = recommendations.map(rec => {
            const iconClass = rec.type === 'WARNING' ? 'fas fa-exclamation-triangle text-danger' :
                             rec.type === 'CAUTION' ? 'fas fa-exclamation-circle text-warning' :
                             rec.type === 'SUCCESS' ? 'fas fa-check-circle text-success' :
                             'fas fa-info-circle text-info';

            const cardClass = rec.type === 'WARNING' ? 'border-danger' :
                             rec.type === 'CAUTION' ? 'border-warning' :
                             rec.type === 'SUCCESS' ? 'border-success' :
                             'border-info';

            return `
                <div class="card ${cardClass} mb-2">
                    <div class="card-body">
                        <div class="d-flex align-items-start">
                            <i class="${iconClass} me-3 mt-1"></i>
                            <div>
                                <h6 class="card-title mb-1">${rec.title}</h6>
                                <p class="card-text mb-1">${rec.message}</p>
                                <small class="text-muted"><strong>Recommended Action:</strong> ${rec.action}</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div class="mb-4">
                <h5><i class="fas fa-lightbulb me-2"></i>AI Recommendations</h5>
                ${recommendationCards}
            </div>
        `;
    }

    createDuplicationAnalysisSection(duplicationAnalysis) {
        const highRisk = duplicationAnalysis.high_risk || [];
        const mediumRisk = duplicationAnalysis.medium_risk || [];
        const lowRisk = duplicationAnalysis.low_risk || [];

        if (highRisk.length === 0 && mediumRisk.length === 0 && lowRisk.length === 0) {
            return `
                <div class="mb-4">
                    <h5><i class="fas fa-shield-alt me-2 text-success"></i>Duplication Analysis</h5>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        No potential duplicates detected. Safe to proceed with work order creation.
                    </div>
                </div>
            `;
        }

        let duplicatesHtml = '';

        if (highRisk.length > 0) {
            duplicatesHtml += `
                <div class="alert alert-danger">
                    <h6><i class="fas fa-exclamation-triangle me-2"></i>High Risk Duplicates (${highRisk.length})</h6>
                    ${highRisk.map(dup => this.createDuplicateCard(dup, 'danger')).join('')}
                </div>
            `;
        }

        if (mediumRisk.length > 0) {
            duplicatesHtml += `
                <div class="alert alert-warning">
                    <h6><i class="fas fa-exclamation-circle me-2"></i>Medium Risk Duplicates (${mediumRisk.length})</h6>
                    ${mediumRisk.map(dup => this.createDuplicateCard(dup, 'warning')).join('')}
                </div>
            `;
        }

        if (lowRisk.length > 0) {
            duplicatesHtml += `
                <div class="alert alert-info">
                    <h6><i class="fas fa-info-circle me-2"></i>Low Risk Similar Work Orders (${lowRisk.length})</h6>
                    ${lowRisk.map(dup => this.createDuplicateCard(dup, 'info')).join('')}
                </div>
            `;
        }

        return `
            <div class="mb-4">
                <h5><i class="fas fa-search me-2"></i>Duplication Analysis</h5>
                ${duplicatesHtml}
            </div>
        `;
    }

    createDuplicateCard(duplicate, alertType) {
        const wo = duplicate.workorder;
        const similarity = Math.round(duplicate.similarity_score * 100);
        const matchingKeywords = duplicate.matching_keywords || [];
        const riskFactors = duplicate.risk_factors || [];

        return `
            <div class="card border-${alertType} mt-2">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="card-title">
                                Work Order ${wo.wonum}
                                <span class="badge bg-${alertType} ms-2">${similarity}% Similar</span>
                            </h6>
                            <p class="card-text">${wo.description}</p>
                            ${wo.longdescription ? `<p class="card-text"><small class="text-muted">${wo.longdescription}</small></p>` : ''}

                            <div class="row">
                                <div class="col-md-6">
                                    <small><strong>Status:</strong> <span class="badge bg-secondary">${wo.status}</span></small><br>
                                    <small><strong>Priority:</strong> ${wo.priority || 'N/A'}</small><br>
                                    <small><strong>Work Type:</strong> ${wo.worktype || 'N/A'}</small>
                                </div>
                                <div class="col-md-6">
                                    <small><strong>Report Date:</strong> ${wo.reportdate ? new Date(wo.reportdate).toLocaleDateString() : 'N/A'}</small><br>
                                    <small><strong>Scheduled Start:</strong> ${wo.schedstart ? new Date(wo.schedstart).toLocaleDateString() : 'N/A'}</small>
                                </div>
                            </div>

                            ${matchingKeywords.length > 0 ? `
                                <div class="mt-2">
                                    <small><strong>Matching Keywords:</strong></small><br>
                                    ${matchingKeywords.map(keyword => `<span class="badge bg-light text-dark me-1">${keyword}</span>`).join('')}
                                </div>
                            ` : ''}

                            ${riskFactors.length > 0 ? `
                                <div class="mt-2">
                                    <small><strong>Risk Factors:</strong></small><br>
                                    ${riskFactors.map(factor => `<small class="text-${alertType} d-block">• ${factor}</small>`).join('')}
                                </div>
                            ` : ''}
                        </div>
                        <div class="ms-3">
                            <button type="button" class="btn btn-sm btn-outline-primary view-workorder-btn" data-wonum="${wo.wonum}">
                                <i class="fas fa-eye"></i> View
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    createExistingWorkOrdersSection(existingWorkorders, isMobile = false) {
        const workorderCards = existingWorkorders.map(wo => `
            <div class="card mb-2">
                <div class="card-body ${isMobile ? 'py-2' : ''}">
                    <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                            <h6 class="card-title ${isMobile ? 'mb-1' : ''}">${isMobile ? 'WO' : 'Work Order'} ${wo.wonum}</h6>
                            <p class="card-text ${isMobile ? 'small mb-1' : ''}">${wo.description}</p>
                            ${isMobile ? `
                                <div class="d-flex gap-2 flex-wrap">
                                    <span class="badge bg-secondary">${wo.status}</span>
                                    ${wo.priority ? `<span class="badge bg-info">P${wo.priority}</span>` : ''}
                                    ${wo.worktype ? `<span class="badge bg-light text-dark">${wo.worktype}</span>` : ''}
                                </div>
                            ` : `
                                <div class="row">
                                    <div class="col-md-6">
                                        <small><strong>Status:</strong> <span class="badge bg-secondary">${wo.status}</span></small><br>
                                        <small><strong>Priority:</strong> ${wo.priority || 'N/A'}</small>
                                    </div>
                                    <div class="col-md-6">
                                        <small><strong>Work Type:</strong> ${wo.worktype || 'N/A'}</small><br>
                                        <small><strong>Report Date:</strong> ${wo.reportdate ? new Date(wo.reportdate).toLocaleDateString() : 'N/A'}</small>
                                    </div>
                                </div>
                            `}
                        </div>
                        <div class="ms-2">
                            <button type="button" class="btn btn-sm btn-outline-primary view-workorder-btn" data-wonum="${wo.wonum}">
                                <i class="fas fa-eye"></i>${isMobile ? '' : ' View'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');

        if (isMobile) {
            return workorderCards; // Return just the cards for mobile accordion
        }

        return `
            <div class="mb-4">
                <h5><i class="fas fa-list me-2"></i>Existing Open Work Orders (${existingWorkorders.length})</h5>
                <div style="max-height: 300px; overflow-y: auto;">
                    ${workorderCards}
                </div>
            </div>
        `;
    }

    createIntendedWorkOrderSection(formData, isMobile = false) {
        const content = `
            <div class="card border-primary">
                <div class="card-body ${isMobile ? 'py-2' : ''}">
                    <h6 class="card-title ${isMobile ? 'mb-1' : ''}">New Work Order</h6>
                    <p class="card-text ${isMobile ? 'small mb-1' : ''}"><strong>Description:</strong> ${formData.description || 'No description provided'}</p>
                    ${formData.longdescription && !isMobile ? `<p class="card-text"><strong>Long Description:</strong> ${formData.longdescription}</p>` : ''}
                    ${isMobile ? `
                        <div class="d-flex gap-2 flex-wrap">
                            ${formData.worktype ? `<span class="badge bg-primary">${formData.worktype}</span>` : ''}
                            ${formData.priority ? `<span class="badge bg-info">P${formData.priority}</span>` : ''}
                            <span class="badge bg-light text-dark">${formData.assetnum}</span>
                        </div>
                    ` : `
                        <div class="row">
                            <div class="col-md-6">
                                <small><strong>Work Type:</strong> ${formData.worktype || 'N/A'}</small><br>
                                <small><strong>Priority:</strong> ${formData.priority || 'N/A'}</small>
                            </div>
                            <div class="col-md-6">
                                <small><strong>Asset:</strong> ${formData.assetnum || 'N/A'}</small><br>
                                <small><strong>Site:</strong> ${formData.siteid || 'N/A'}</small>
                            </div>
                        </div>
                    `}
                </div>
            </div>
        `;

        if (isMobile) {
            return content; // Return just the card for mobile accordion
        }

        return `
            <div class="mb-4">
                <h5><i class="fas fa-plus me-2"></i>Your Intended Work Order</h5>
                ${content}
            </div>
        `;
    }

    setupAIInsightsEventListeners(analysisResult, asset, formData) {
        // Proceed with creation button
        const proceedBtn = document.getElementById('proceedWithCreationBtn');
        if (proceedBtn) {
            proceedBtn.addEventListener('click', () => {
                this.proceedWithWorkOrderCreation(analysisResult, asset, formData);
            });
        }

        // Review existing work orders button
        const reviewBtn = document.getElementById('reviewExistingBtn');
        if (reviewBtn) {
            reviewBtn.addEventListener('click', () => {
                this.openWorkOrderManagement(asset.siteid, asset.assetnum);
            });
        }

        // View individual work order buttons
        const viewButtons = document.querySelectorAll('.view-workorder-btn');
        viewButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const wonum = e.target.closest('.view-workorder-btn').dataset.wonum;
                this.openWorkOrderDetails(wonum);
            });
        });

        // Store analysis result for later use
        window.currentAIAnalysis = analysisResult;
    }

    async proceedWithWorkOrderCreation(analysisResult, asset, formData) {
        try {
            console.log('🔧 PROCEED: Starting work order creation process...');
            console.log('🔧 PROCEED: Form data:', formData);

            // Add acknowledgment note if there are high-risk duplicates
            const duplicationAnalysis = analysisResult.duplication_analysis || {};
            if (duplicationAnalysis.risk_level === 'HIGH') {
                const highRiskCount = duplicationAnalysis.high_risk?.length || 0;
                const acknowledgmentNote = `AI Analysis: User acknowledged ${highRiskCount} potential duplicate(s) and chose to proceed. Similarity score: ${Math.round((analysisResult.kpis?.highest_similarity_score || 0) * 100)}%`;

                // Add to long description
                if (formData.longdescription) {
                    formData.longdescription += `\n\n${acknowledgmentNote}`;
                } else {
                    formData.longdescription = acknowledgmentNote;
                }
                console.log('🔧 PROCEED: Added AI acknowledgment note');
            }

            // Update user decision in database
            if (analysisResult.analysis_id) {
                console.log('🔧 PROCEED: Updating AI analysis decision...');
                await this.updateAIAnalysisDecision(analysisResult.analysis_id, 'proceed');
                console.log('🔧 PROCEED: AI analysis decision updated');
            }

            // Close the AI insights modal AFTER creating the work order
            console.log('🔧 PROCEED: Creating work order...');
            await this.createWorkOrderWithoutAI(formData);
            console.log('🔧 PROCEED: Work order creation completed');

            // Close the AI insights modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('aiInsightsModal'));
            if (modal) {
                modal.hide();
                console.log('🔧 PROCEED: AI insights modal closed');
            }

        } catch (error) {
            console.error('❌ PROCEED: Error in work order creation process:', error);
            this.showAlert('Failed to proceed with work order creation: ' + error.message, 'error');
        }
    }

    async updateAIAnalysisDecision(analysisId, decision, createdWorkorderNum = null) {
        try {
            const response = await fetch('/api/ai-analysis/update-decision', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    analysis_id: analysisId,
                    user_decision: decision,
                    created_workorder_num: createdWorkorderNum
                })
            });

            if (!response.ok) {
                console.warn('Failed to update AI analysis decision');
            }
        } catch (error) {
            console.warn('Error updating AI analysis decision:', error);
        }
    }

    openWorkOrderManagement(siteid, assetnum = null) {
        // Open work order management with site and asset filters
        let url = `/enhanced-workorders?siteid=${siteid}&auto_search=true`;

        // Add asset number filter if provided
        if (assetnum) {
            url += `&assetnum=${assetnum}`;
        }

        window.open(url, '_blank');
    }

    openWorkOrderDetails(wonum) {
        // Open work order details page
        const url = `/workorder/${wonum}`;
        window.open(url, '_blank');
    }

    async createWorkOrderWithoutAI(formData) {
        // This is the original work order creation logic without AI analysis
        try {
            console.log('🔧 CREATE WO: Starting work order creation...');
            console.log('🔧 CREATE WO: Form data:', formData);

            this.showAlert('Creating work order...', 'info');

            const response = await fetch('/api/asset/create-workorder', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            });

            console.log('🔧 CREATE WO: API response status:', response.status);
            const result = await response.json();
            console.log('🔧 CREATE WO: API response data:', result);

            if (result.success) {
                console.log('🎉 CREATE WO: Work order created successfully!');
                console.log('🎉 CREATE WO: Work order number:', result.wonum);

                // Update AI analysis with created work order number
                if (window.currentAIAnalysis?.analysis_id && result.wonum) {
                    console.log('🔧 CREATE WO: Updating AI analysis with work order number...');
                    await this.updateAIAnalysisDecision(
                        window.currentAIAnalysis.analysis_id,
                        'proceed',
                        result.wonum
                    );
                    console.log('🔧 CREATE WO: AI analysis updated');
                }

                console.log('🔧 CREATE WO: Showing success response...');
                this.showWorkOrderCreationSuccess(result);

                console.log('✅ CREATE WO: Process completed successfully');
            } else {
                console.error('❌ CREATE WO: Work order creation failed:', result.error);
                this.showAlert(result.error || 'Failed to create work order', 'error');
            }
        } catch (error) {
            console.error('❌ CREATE WO: Network error:', error);
            this.showAlert('Network error occurred while creating work order: ' + error.message, 'error');
        }
    }

    initializeDropdowns() {
        // Simple dropdown initialization - let Bootstrap handle positioning
        // Just ensure proper z-index
        setTimeout(() => {
            const dropdownElements = document.querySelectorAll('[data-bs-toggle="dropdown"]');
            dropdownElements.forEach(element => {
                element.addEventListener('shown.bs.dropdown', function() {
                    const menu = this.nextElementSibling;
                    if (menu && menu.classList.contains('dropdown-menu')) {
                        menu.style.zIndex = '9999';
                    }
                });
            });
        }, 100);
    }
}

// Global functions for HTML onclick handlers
function showAssetDetails(assetnum, siteid) {
    if (window.assetManager) {
        window.assetManager.showAssetDetails(assetnum, siteid);
    }
}

function showRelatedRecords(assetnum, siteid) {
    console.log(`🔧 BUTTON CLICKED: ${assetnum} in ${siteid}`);
    console.log('🔧 DEBUG: About to call showRelatedRecordsModal');
    try {
        showRelatedRecordsModal(assetnum, siteid);
        console.log('🔧 DEBUG: showRelatedRecordsModal called successfully');
    } catch (error) {
        console.error('🔧 ERROR: Failed to call showRelatedRecordsModal:', error);
        alert('Error: ' + error.message);
    }
}

function showAssetActions(assetnum, siteid) {
    if (window.assetManager) {
        window.assetManager.showAssetActions(assetnum, siteid);
    }
}

function clearAssetCache() {
    if (window.assetManager) {
        window.assetManager.clearAssetCache();
    }
}

function exportAssetResults() {
    if (window.assetManager) {
        window.assetManager.exportAssetResults();
    }
}

// Create operation functions - Direct asset functions (called from action buttons)
function showCreateWorkOrderModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Create Work Order Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showCreateWorkOrderModal({assetnum, siteid});
    }
}

function showCreateServiceRequestModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Create Service Request Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showCreateServiceRequestModal({assetnum, siteid});
    }
}

function showCreateIncidentModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Create Incident Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showCreateIncidentModal({assetnum, siteid});
    }
}

function showCreateProblemModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Create Problem Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showCreateProblemModal({assetnum, siteid});
    }
}

function showCreateChangeModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Create Change Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showCreateChangeModal({assetnum, siteid});
    }
}

function showCreateReleaseModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Create Release Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showCreateReleaseModal({assetnum, siteid});
    }
}

function showCreateInspectionModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Create Unscheduled Inspection Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showCreateInspectionModal({assetnum, siteid});
    }
}

function showReportDowntimeModalForAsset(assetnum, siteid) {
    console.log(`🔧 Opening Report Downtime Modal for ${assetnum}`);
    if (window.assetManager) {
        window.assetManager.showReportDowntimeModal({assetnum, siteid});
    }
}

function getSelectedAssets() {
    const checkboxes = document.querySelectorAll('.asset-checkbox:checked');
    return Array.from(checkboxes).map(cb => ({
        assetnum: cb.value,
        siteid: cb.dataset.siteid
    }));
}

function executeAssetAction(actionId, assetnum, siteid) {
    console.log(`🔧 Executing action ${actionId} for asset ${assetnum} in site ${siteid}`);

    // Prevent event bubbling
    if (typeof event !== 'undefined') {
        event.preventDefault();
        event.stopPropagation();
    }

    // Only handle view_related_records action
    if (actionId === 'view_related_records') {
        console.log(`📋 Opening related records modal for ${assetnum}`);
        showRelatedRecordsModal(assetnum, siteid);
    } else {
        console.warn(`Unknown action: ${actionId}`);
    }
}

function showRelatedRecordsModal(assetnum, siteid) {
    console.log(`🔍 ENHANCED MODAL: Showing related records for asset ${assetnum} in site ${siteid}`);
    console.log('🔍 DEBUG: Creating enhanced modal with side navigation');

    // Create modal HTML with enhanced tabbed interface and side navigation
    const modalHtml = `
        <div class="modal fade" id="relatedRecordsModal" tabindex="-1" aria-labelledby="relatedRecordsModalLabel" aria-hidden="true">
            <div class="modal-dialog modal-xl">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title" id="relatedRecordsModalLabel">
                            <i class="fas fa-list-alt"></i> Related Records - ${assetnum}
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body p-0">
                        <div class="loading-spinner text-center p-4">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="mt-2">Loading related records...</p>
                        </div>
                        <div class="related-records-content" style="display: none;">
                            <div class="row g-0">
                                <!-- Modern Side Navigation -->
                                <div class="col-md-3 border-end">
                                    <div class="modern-side-nav">
                                        <!-- Header -->
                                        <div class="side-nav-header p-3 bg-gradient-primary text-white">
                                            <h6 class="mb-0 fw-bold">
                                                <i class="fas fa-layer-group me-2"></i>Related Records
                                            </h6>
                                            <small class="opacity-75">Asset: ${assetnum}</small>
                                        </div>

                                        <!-- Navigation Items -->
                                        <div class="side-nav-body p-2">
                                            <div class="nav flex-column" id="v-pills-tab" role="tablist" aria-orientation="vertical">
                                                <!-- Work Orders -->
                                                <button class="nav-link modern-nav-item active d-flex align-items-center p-3 mb-2" id="v-pills-workorders-tab" data-bs-toggle="pill" data-bs-target="#v-pills-workorders" type="button" role="tab" aria-controls="v-pills-workorders" aria-selected="true">
                                                    <div class="nav-icon me-3">
                                                        <i class="fas fa-wrench"></i>
                                                    </div>
                                                    <div class="nav-content flex-grow-1">
                                                        <div class="nav-title fw-semibold">Work Orders</div>
                                                        <div class="nav-subtitle text-muted small">Maintenance & Repairs</div>
                                                    </div>
                                                    <div class="nav-badge">
                                                        <span class="badge bg-primary rounded-pill" id="workorders-count">0</span>
                                                    </div>
                                                </button>

                                                <!-- Service Requests -->
                                                <button class="nav-link modern-nav-item d-flex align-items-center p-3 mb-2" id="v-pills-service-requests-tab" data-bs-toggle="pill" data-bs-target="#v-pills-service-requests" type="button" role="tab" aria-controls="v-pills-service-requests" aria-selected="false">
                                                    <div class="nav-icon me-3">
                                                        <i class="fas fa-ticket-alt"></i>
                                                    </div>
                                                    <div class="nav-content flex-grow-1">
                                                        <div class="nav-title fw-semibold">Service Requests</div>
                                                        <div class="nav-subtitle text-muted small">User Requests & Issues</div>
                                                    </div>
                                                    <div class="nav-badge">
                                                        <span class="badge bg-info rounded-pill" id="service-requests-count">0</span>
                                                    </div>
                                                </button>

                                                <!-- Asset Status -->
                                                <button class="nav-link modern-nav-item d-flex align-items-center p-3 mb-2" id="v-pills-asset-status-tab" data-bs-toggle="pill" data-bs-target="#v-pills-asset-status" type="button" role="tab" aria-controls="v-pills-asset-status" aria-selected="false">
                                                    <div class="nav-icon me-3">
                                                        <i class="fas fa-chart-pie"></i>
                                                    </div>
                                                    <div class="nav-content flex-grow-1">
                                                        <div class="nav-title fw-semibold">Asset Overview</div>
                                                        <div class="nav-subtitle text-muted small">Status & Analytics</div>
                                                    </div>
                                                    <div class="nav-badge">
                                                        <span class="badge bg-success rounded-pill">1</span>
                                                    </div>
                                                </button>
                                            </div>
                                        </div>

                                        <!-- Footer -->
                                        <div class="side-nav-footer p-3 border-top bg-light">
                                            <small class="text-muted d-flex align-items-center">
                                                <i class="fas fa-clock me-2"></i>
                                                <span id="last-updated">Loading...</span>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                                <!-- Main Content -->
                                <div class="col-md-9">
                                    <div class="tab-content p-3" id="v-pills-tabContent">
                                        <!-- Work Orders Tab -->
                                        <div class="tab-pane fade show active" id="v-pills-workorders" role="tabpanel" aria-labelledby="v-pills-workorders-tab">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Work Orders</h6>
                                                <div class="pagination-info">
                                                    <small class="text-muted" id="workorders-pagination-info">Loading...</small>
                                                </div>
                                            </div>
                                            <div class="workorders-content">
                                                <!-- Desktop View -->
                                                <div class="d-none d-md-block">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover table-sm">
                                                            <thead class="table-dark">
                                                                <tr>
                                                                    <th>WO Number</th>
                                                                    <th>PM Number</th>
                                                                    <th>Description</th>
                                                                    <th>Site</th>
                                                                    <th>Status</th>
                                                                    <th>Priority</th>
                                                                    <th>Work Type</th>
                                                                    <th>Scheduled Date</th>
                                                                    <th>Lead Craft</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="workorders-table-body">
                                                                <!-- Work orders will be populated here -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <!-- Mobile View -->
                                                <div class="d-md-none">
                                                    <div id="workorders-cards-container">
                                                        <!-- Work order cards will be populated here -->
                                                    </div>
                                                </div>
                                                <!-- Pagination -->
                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <div class="pagination-controls">
                                                        <nav aria-label="Work orders pagination">
                                                            <ul class="pagination pagination-sm mb-0" id="workorders-pagination">
                                                                <!-- Pagination will be populated here -->
                                                            </ul>
                                                        </nav>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Service Requests Tab -->
                                        <div class="tab-pane fade" id="v-pills-service-requests" role="tabpanel" aria-labelledby="v-pills-service-requests-tab">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Service Requests</h6>
                                                <div class="pagination-info">
                                                    <small class="text-muted" id="service-requests-pagination-info">Loading...</small>
                                                </div>
                                            </div>
                                            <div class="service-requests-content">
                                                <!-- Desktop View -->
                                                <div class="d-none d-md-block">
                                                    <div class="table-responsive">
                                                        <table class="table table-hover table-sm">
                                                            <thead class="table-dark">
                                                                <tr>
                                                                    <th>Ticket ID</th>
                                                                    <th>Description</th>
                                                                    <th>Site</th>
                                                                    <th>Status</th>
                                                                    <th>Priority</th>
                                                                    <th>Category</th>
                                                                    <th>Reported Date</th>
                                                                    <th>Reported By</th>
                                                                    <th>Assigned To</th>
                                                                </tr>
                                                            </thead>
                                                            <tbody id="service-requests-table-body">
                                                                <!-- Service requests will be populated here -->
                                                            </tbody>
                                                        </table>
                                                    </div>
                                                </div>
                                                <!-- Mobile View -->
                                                <div class="d-md-none">
                                                    <div id="service-requests-cards-container">
                                                        <!-- Service request cards will be populated here -->
                                                    </div>
                                                </div>
                                                <!-- Pagination -->
                                                <div class="d-flex justify-content-between align-items-center mt-3">
                                                    <div class="pagination-controls">
                                                        <nav aria-label="Service requests pagination">
                                                            <ul class="pagination pagination-sm mb-0" id="service-requests-pagination">
                                                                <!-- Pagination will be populated here -->
                                                            </ul>
                                                        </nav>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- Asset Status Tab -->
                                        <div class="tab-pane fade" id="v-pills-asset-status" role="tabpanel" aria-labelledby="v-pills-asset-status-tab">
                                            <div class="d-flex justify-content-between align-items-center mb-3">
                                                <h6 class="mb-0">Asset Availability & Status</h6>
                                            </div>
                                            <div id="asset-status-content">
                                                <!-- Asset status will be populated here -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Remove existing modal if present
    const existingModal = document.getElementById('relatedRecordsModal');
    if (existingModal) {
        existingModal.remove();
    }

    // Add modal to DOM
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    console.log('🔍 DEBUG: Enhanced modal HTML added to DOM');

    // Show modal using Bootstrap 5
    const modalElement = document.getElementById('relatedRecordsModal');
    console.log('🔍 DEBUG: Modal element found:', modalElement ? 'YES' : 'NO');

    if (modalElement) {
        const modal = new bootstrap.Modal(modalElement);
        modal.show();
        console.log('🔍 DEBUG: Enhanced modal shown');
    } else {
        console.error('🔍 ERROR: Modal element not found!');
    }

    // Initialize pagination state
    window.relatedRecordsState = {
        workorders: { currentPage: 1, totalPages: 0, data: [] },
        serviceRequests: { currentPage: 1, totalPages: 0, data: [] },
        recordsPerPage: 10
    };

    // Fetch related records
    fetchRelatedRecords(assetnum, siteid);
}

function fetchRelatedRecords(assetnum, siteid) {
    console.log(`📡 Fetching related records for asset ${assetnum} using OSLC`);

    fetch(`/oslc/os/mxapiasset/${assetnum}/related-records?siteid=${siteid}`)
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(oslcResponse => {
            console.log('✅ OSLC related records fetched:', oslcResponse);
            // Extract the actual data from OSLC response
            const data = oslcResponse.member && oslcResponse.member.length > 0 ? oslcResponse.member[0] : {};
            displayRelatedRecords(data);
        })
        .catch(error => {
            console.error('❌ Error fetching related records:', error);
            displayRelatedRecordsError(error.message);
        });
}

function displayRelatedRecords(data) {
    console.log('📊 Displaying related records:', data);

    // Hide loading spinner
    const loadingSpinner = document.querySelector('#relatedRecordsModal .loading-spinner');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'none';
    }

    // Show content
    const content = document.querySelector('#relatedRecordsModal .related-records-content');
    if (content) {
        content.style.display = 'block';
    }

    // Store data in global state
    const workorders = data.workorders || [];
    const serviceRequests = data.service_requests || [];

    window.relatedRecordsState.workorders.data = workorders;
    window.relatedRecordsState.serviceRequests.data = serviceRequests;

    // Calculate total pages
    const recordsPerPage = window.relatedRecordsState.recordsPerPage;
    window.relatedRecordsState.workorders.totalPages = Math.ceil(workorders.length / recordsPerPage);
    window.relatedRecordsState.serviceRequests.totalPages = Math.ceil(serviceRequests.length / recordsPerPage);

    // Update counts
    document.getElementById('workorders-count').textContent = workorders.length;
    document.getElementById('service-requests-count').textContent = serviceRequests.length;

    // Display first page of each
    displayWorkOrdersPage(1);
    displayServiceRequestsPage(1);

    // Display asset status
    displayAssetStatus(data.asset || {});

    // Update last updated timestamp
    updateLastUpdatedTime();
}

function displayWorkOrdersPage(page) {
    const state = window.relatedRecordsState.workorders;
    const recordsPerPage = window.relatedRecordsState.recordsPerPage;
    const startIndex = (page - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const pageData = state.data.slice(startIndex, endIndex);

    state.currentPage = page;

    // Update pagination info
    const totalRecords = state.data.length;
    const showingStart = totalRecords > 0 ? startIndex + 1 : 0;
    const showingEnd = Math.min(endIndex, totalRecords);
    document.getElementById('workorders-pagination-info').textContent =
        `Showing ${showingStart}-${showingEnd} of ${totalRecords} records`;

    // Display desktop table
    displayWorkOrdersTable(pageData);

    // Display mobile cards
    displayWorkOrdersCards(pageData);

    // Update pagination controls
    updateWorkOrdersPagination();
}

function displayWorkOrdersTable(workorders) {
    const tableBody = document.getElementById('workorders-table-body');
    if (!tableBody) return;

    if (workorders.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">No work orders found for this asset.</td></tr>';
        return;
    }

    const workordersHtml = workorders.map(wo => `
        <tr>
            <td>
                <a href="/workorder/${wo.wonum}" target="_blank" class="text-decoration-none">
                    <strong class="text-primary">${wo.wonum || 'N/A'}</strong>
                    <i class="fas fa-external-link-alt ms-1 text-muted" style="font-size: 0.8em;"></i>
                </a>
            </td>
            <td>
                <span class="badge bg-secondary">${wo.pmnum || '-'}</span>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 250px;" title="${wo.description || 'No description'}">
                    ${wo.description || 'No description'}
                </div>
            </td>
            <td>
                <span class="badge bg-outline-primary">${wo.siteid || 'N/A'}</span>
            </td>
            <td>
                <span class="badge bg-${getStatusBadgeClass(wo.status)}">${wo.status || 'N/A'}</span>
            </td>
            <td>
                ${wo.priority ? `<span class="badge bg-${getPriorityBadgeClass(wo.priority)}">${wo.priority}</span>` : '-'}
            </td>
            <td>
                <small class="text-muted">${wo.worktype || '-'}</small>
            </td>
            <td>
                <small>${formatDate(wo.schedstart || wo.statusdate)}</small>
            </td>
            <td>
                <small>${wo.lead || wo.leadcraft || '-'}</small>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = workordersHtml;
}

function displayWorkOrdersCards(workorders) {
    const container = document.getElementById('workorders-cards-container');
    if (!container) return;

    if (workorders.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-3">No work orders found for this asset.</div>';
        return;
    }

    const workordersHtml = workorders.map(wo => `
        <div class="card mb-3 shadow-sm border-start border-primary border-3 workorder-card-clickable" data-wonum="${wo.wonum}" style="cursor: pointer;">
            <div class="card-body">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="card-title mb-1">
                            <i class="fas fa-wrench text-primary me-2"></i>
                            <a href="/workorder/${wo.wonum}" target="_blank" class="text-decoration-none">
                                <strong class="text-primary">${wo.wonum || 'N/A'}</strong>
                                <i class="fas fa-external-link-alt ms-1 text-muted" style="font-size: 0.7em;"></i>
                            </a>
                        </h6>
                        ${wo.pmnum ? `<small class="text-muted">PM: <span class="badge bg-secondary">${wo.pmnum}</span></small>` : ''}
                    </div>
                    <span class="badge bg-${getStatusBadgeClass(wo.status)} fs-6">${wo.status || 'N/A'}</span>
                </div>

                <!-- Description -->
                <p class="card-text text-dark mb-3">${wo.description || 'No description'}</p>

                <!-- Details Grid -->
                <div class="row g-2 mb-2">
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            <strong>Site:</strong> ${wo.siteid || 'N/A'}
                        </small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-tools me-1"></i>
                            <strong>Type:</strong> ${wo.worktype || 'N/A'}
                        </small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-calendar me-1"></i>
                            <strong>Scheduled:</strong> ${formatDate(wo.schedstart || wo.statusdate)}
                        </small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-user me-1"></i>
                            <strong>Lead:</strong> ${wo.lead || wo.leadcraft || 'N/A'}
                        </small>
                    </div>
                </div>

                <!-- Priority Badge -->
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        ${wo.priority ? `<span class="badge bg-${getPriorityBadgeClass(wo.priority)}">Priority ${wo.priority}</span>` : ''}
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>Updated: ${formatDate(wo.statusdate)}
                    </small>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = workordersHtml;

    // Add click handlers for mobile cards
    container.querySelectorAll('.workorder-card-clickable').forEach(card => {
        card.addEventListener('click', function(e) {
            // Don't trigger if clicking on a link
            if (e.target.tagName === 'A' || e.target.closest('a')) {
                return;
            }

            const wonum = this.getAttribute('data-wonum');
            if (wonum) {
                window.open(`/workorder/${wonum}`, '_blank');
            }
        });
    });
}

function displayServiceRequestsPage(page) {
    const state = window.relatedRecordsState.serviceRequests;
    const recordsPerPage = window.relatedRecordsState.recordsPerPage;
    const startIndex = (page - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const pageData = state.data.slice(startIndex, endIndex);

    state.currentPage = page;

    // Update pagination info
    const totalRecords = state.data.length;
    const showingStart = totalRecords > 0 ? startIndex + 1 : 0;
    const showingEnd = Math.min(endIndex, totalRecords);
    document.getElementById('service-requests-pagination-info').textContent =
        `Showing ${showingStart}-${showingEnd} of ${totalRecords} records`;

    // Display desktop table
    displayServiceRequestsTable(pageData);

    // Display mobile cards
    displayServiceRequestsCards(pageData);

    // Update pagination controls
    updateServiceRequestsPagination();
}

function displayServiceRequestsTable(serviceRequests) {
    const tableBody = document.getElementById('service-requests-table-body');
    if (!tableBody) return;

    if (serviceRequests.length === 0) {
        tableBody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">No service requests found for this asset.</td></tr>';
        return;
    }

    const serviceRequestsHtml = serviceRequests.map(sr => `
        <tr>
            <td>
                <strong class="text-info">${sr.ticketid || 'N/A'}</strong>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 250px;" title="${sr.description || 'No description'}">
                    ${sr.description || 'No description'}
                </div>
            </td>
            <td>
                <span class="badge bg-outline-primary">${sr.siteid || 'N/A'}</span>
            </td>
            <td>
                <span class="badge bg-${getStatusBadgeClass(sr.status)}">${sr.status || 'N/A'}</span>
            </td>
            <td>
                ${sr.reportedpriority ? `<span class="badge bg-${getPriorityBadgeClass(sr.reportedpriority)}">${sr.reportedpriority}</span>` : '-'}
            </td>
            <td>
                <small class="text-muted">${sr.classstructureid || sr.category || '-'}</small>
            </td>
            <td>
                <small>${formatDate(sr.reportdate || sr.statusdate)}</small>
            </td>
            <td>
                <small>${sr.reportedby || '-'}</small>
            </td>
            <td>
                <small>${sr.assignedto || sr.owner || '-'}</small>
            </td>
        </tr>
    `).join('');

    tableBody.innerHTML = serviceRequestsHtml;
}

function displayServiceRequestsCards(serviceRequests) {
    const container = document.getElementById('service-requests-cards-container');
    if (!container) return;

    if (serviceRequests.length === 0) {
        container.innerHTML = '<div class="text-center text-muted p-3">No service requests found for this asset.</div>';
        return;
    }

    const serviceRequestsHtml = serviceRequests.map(sr => `
        <div class="card mb-3 shadow-sm border-start border-info border-3">
            <div class="card-body">
                <!-- Header -->
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="card-title mb-1">
                            <i class="fas fa-ticket-alt text-info me-2"></i>
                            <strong>${sr.ticketid || 'N/A'}</strong>
                        </h6>
                        ${sr.classstructureid || sr.category ? `<small class="text-muted">Category: <span class="badge bg-light text-dark">${sr.classstructureid || sr.category}</span></small>` : ''}
                    </div>
                    <span class="badge bg-${getStatusBadgeClass(sr.status)} fs-6">${sr.status || 'N/A'}</span>
                </div>

                <!-- Description -->
                <p class="card-text text-dark mb-3">${sr.description || 'No description'}</p>

                <!-- Details Grid -->
                <div class="row g-2 mb-2">
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-map-marker-alt me-1"></i>
                            <strong>Site:</strong> ${sr.siteid || 'N/A'}
                        </small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-user-check me-1"></i>
                            <strong>Assigned:</strong> ${sr.assignedto || sr.owner || 'Unassigned'}
                        </small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-calendar me-1"></i>
                            <strong>Reported:</strong> ${formatDate(sr.reportdate || sr.statusdate)}
                        </small>
                    </div>
                    <div class="col-6">
                        <small class="text-muted d-block">
                            <i class="fas fa-user me-1"></i>
                            <strong>Reporter:</strong> ${sr.reportedby || 'N/A'}
                        </small>
                    </div>
                </div>

                <!-- Priority Badge -->
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        ${sr.reportedpriority ? `<span class="badge bg-${getPriorityBadgeClass(sr.reportedpriority)}">Priority ${sr.reportedpriority}</span>` : ''}
                    </div>
                    <small class="text-muted">
                        <i class="fas fa-clock me-1"></i>Updated: ${formatDate(sr.statusdate)}
                    </small>
                </div>
            </div>
        </div>
    `).join('');

    container.innerHTML = serviceRequestsHtml;
}

function updateWorkOrdersPagination() {
    const state = window.relatedRecordsState.workorders;
    const paginationContainer = document.getElementById('workorders-pagination');
    if (!paginationContainer) return;

    const totalPages = state.totalPages;
    const currentPage = state.currentPage;

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // Previous button
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayWorkOrdersPage(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="displayWorkOrdersPage(${i}); return false;">${i}</a>
            </li>
        `;
    }

    // Next button
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayWorkOrdersPage(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    paginationContainer.innerHTML = paginationHtml;
}

function updateServiceRequestsPagination() {
    const state = window.relatedRecordsState.serviceRequests;
    const paginationContainer = document.getElementById('service-requests-pagination');
    if (!paginationContainer) return;

    const totalPages = state.totalPages;
    const currentPage = state.currentPage;

    if (totalPages <= 1) {
        paginationContainer.innerHTML = '';
        return;
    }

    let paginationHtml = '';

    // Previous button
    paginationHtml += `
        <li class="page-item ${currentPage === 1 ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayServiceRequestsPage(${currentPage - 1}); return false;">
                <i class="fas fa-chevron-left"></i>
            </a>
        </li>
    `;

    // Page numbers
    const startPage = Math.max(1, currentPage - 2);
    const endPage = Math.min(totalPages, currentPage + 2);

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `
            <li class="page-item ${i === currentPage ? 'active' : ''}">
                <a class="page-link" href="#" onclick="displayServiceRequestsPage(${i}); return false;">${i}</a>
            </li>
        `;
    }

    // Next button
    paginationHtml += `
        <li class="page-item ${currentPage === totalPages ? 'disabled' : ''}">
            <a class="page-link" href="#" onclick="displayServiceRequestsPage(${currentPage + 1}); return false;">
                <i class="fas fa-chevron-right"></i>
            </a>
        </li>
    `;

    paginationContainer.innerHTML = paginationHtml;
}

function displayAssetStatus(asset) {
    const container = document.getElementById('asset-status-content');
    if (!container) return;

    const statusHtml = `
        <div class="row g-3">
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-info-circle text-info me-2"></i>Asset Information
                        </h6>
                        <div class="row g-2">
                            <div class="col-6"><strong>Asset Number:</strong></div>
                            <div class="col-6">${asset.assetnum || 'N/A'}</div>
                            <div class="col-6"><strong>Description:</strong></div>
                            <div class="col-6">${asset.description || 'N/A'}</div>
                            <div class="col-6"><strong>Status:</strong></div>
                            <div class="col-6">
                                <span class="badge bg-${getStatusBadgeClass(asset.status)}">${asset.status || 'N/A'}</span>
                            </div>
                            <div class="col-6"><strong>Site:</strong></div>
                            <div class="col-6">${asset.siteid || 'N/A'}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card h-100">
                    <div class="card-body">
                        <h6 class="card-title">
                            <i class="fas fa-chart-line text-success me-2"></i>Availability Summary
                        </h6>
                        <div class="row g-2">
                            <div class="col-6"><strong>Total Work Orders:</strong></div>
                            <div class="col-6">${window.relatedRecordsState.workorders.data.length}</div>
                            <div class="col-6"><strong>Service Requests:</strong></div>
                            <div class="col-6">${window.relatedRecordsState.serviceRequests.data.length}</div>
                            <div class="col-6"><strong>Last Updated:</strong></div>
                            <div class="col-6">${formatDate(new Date().toISOString())}</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    container.innerHTML = statusHtml;
}

function updateLastUpdatedTime() {
    const lastUpdatedElement = document.getElementById('last-updated');
    if (lastUpdatedElement) {
        const now = new Date();
        const timeString = now.toLocaleTimeString('en-US', {
            hour: '2-digit',
            minute: '2-digit',
            hour12: true
        });
        lastUpdatedElement.textContent = `Updated at ${timeString}`;
    }
}

function displayRelatedRecordsError(errorMessage) {
    // Hide loading spinner
    const loadingSpinner = document.querySelector('#relatedRecordsModal .loading-spinner');
    if (loadingSpinner) {
        loadingSpinner.style.display = 'none';
    }

    // Show error message
    const modalBody = document.querySelector('#relatedRecordsModal .modal-body');
    if (modalBody) {
        modalBody.innerHTML = `
            <div class="alert alert-danger m-3">
                <i class="fas fa-exclamation-triangle"></i>
                <strong>Error:</strong> ${errorMessage}
            </div>
        `;
    }
}

function getStatusBadgeClass(status) {
    if (!status) return 'secondary';
    const statusLower = status.toLowerCase();
    if (statusLower.includes('comp') || statusLower.includes('close')) return 'success';
    if (statusLower.includes('inprg') || statusLower.includes('progress')) return 'primary';
    if (statusLower.includes('wappr') || statusLower.includes('pending')) return 'warning';
    if (statusLower.includes('cancel')) return 'danger';
    return 'secondary';
}

function getPriorityBadgeClass(priority) {
    if (!priority) return 'secondary';
    const priorityNum = parseInt(priority);
    if (priorityNum <= 1) return 'danger';
    if (priorityNum <= 2) return 'warning';
    if (priorityNum <= 3) return 'info';
    return 'secondary';
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    } catch (e) {
        return dateString;
    }
}

// OLD DUPLICATE FUNCTIONS REMOVED - Using enhanced versions above

function getStatusClass(status) {
    if (!status) return 'secondary';

    const statusUpper = status.toUpperCase();
    switch (statusUpper) {
        case 'COMP': case 'COMPLETED': return 'success';
        case 'INPRG': case 'IN PROGRESS': return 'primary';
        case 'APPR': case 'APPROVED': return 'info';
        case 'WMATL': case 'WAITING FOR MATERIAL': return 'warning';
        case 'CLOSE': case 'CLOSED': return 'dark';
        case 'CANCEL': case 'CANCELLED': return 'danger';
        default: return 'secondary';
    }
}

function getPriorityClass(priority) {
    if (!priority) return 'secondary';

    const priorityUpper = priority.toString().toUpperCase();
    switch (priorityUpper) {
        case '1': case 'HIGH': case 'URGENT': return 'danger';
        case '2': case 'MEDIUM': return 'warning';
        case '3': case 'LOW': return 'success';
        default: return 'secondary';
    }
}

function formatDate(dateString) {
    if (!dateString) return 'N/A';

    try {
        const date = new Date(dateString);
        return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
    } catch (e) {
        return dateString;
    }
}

// Initialize when DOM is ready
let assetManager;
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Asset Management page loaded');
    assetManager = new AssetManagement();

    // Make it globally accessible for onclick handlers
    window.assetManager = assetManager;
});
