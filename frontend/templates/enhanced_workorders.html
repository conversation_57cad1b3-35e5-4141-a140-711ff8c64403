{% extends 'base.html' %}

{% block title %}Enhanced Work Orders - <PERSON><PERSON>{% endblock %}

{% block content %}

<style>
/* Materials Badge Styling */
.materials-check-container {
    min-width: 80px;
    text-align: center;
}

.materials-check-btn {
    border: 1px solid #198754; /* Green border */
    background-color: #198754; /* Green background */
    color: white; /* White text and icon color */
    transition: all 0.2s ease;
}

.materials-check-btn:hover {
    background-color: #6c757d; /* Grey background on hover */
    border-color: #6c757d; /* Grey border on hover */
    color: white; /* Keep text and icon color white */
}

.materials-check-btn i, .materials-check-btn span {
    transition: color 0.2s ease, transform 0.2s ease;
}

/* Labor Hours Badge Styling */
.labor-check-container {
    min-width: 80px;
    text-align: center;
}

.labor-check-btn {
    border: 1px solid #fd7e14; /* Orange border */
    background-color: #fd7e14; /* Orange background */
    color: white; /* White text and icon color */
    transition: all 0.2s ease;
}

.labor-check-btn:hover {
    background-color: #6c757d; /* Grey background on hover */
    border-color: #6c757d; /* Grey border on hover */
    color: white; /* Keep text and icon color white */
}

.labor-check-btn i, .labor-check-btn span {
    transition: color 0.2s ease, transform 0.2s ease;
}

/* Labor Cost Badge Styling */
.labor-cost-check-container {
    min-width: 80px;
    text-align: center;
}

.labor-cost-check-btn {
    border: 1px solid #e67e22; /* Darker orange border */
    background-color: #e67e22; /* Darker orange background */
    color: white; /* White text and icon color */
    transition: all 0.2s ease;
}

.labor-cost-check-btn:hover {
    background-color: #6c757d; /* Grey background on hover */
    border-color: #6c757d; /* Grey border on hover */
    color: white; /* Keep text and icon color white */
}

.labor-cost-check-btn i, .labor-cost-check-btn span {
    transition: color 0.2s ease, transform 0.2s ease;
}

/* Material Cost Badge Styling */
.material-cost-check-container {
    min-width: 80px;
    text-align: center;
}

.material-cost-check-btn {
    border: 1px solid #28a745; /* Green border */
    background-color: #28a745; /* Green background */
    color: white; /* White text and icon color */
    transition: all 0.2s ease;
}

.material-cost-check-btn:hover {
    background-color: #6c757d; /* Grey background on hover */
    border-color: #6c757d; /* Grey border on hover */
    color: white; /* Keep text and icon color white */
}

.material-cost-check-btn i, .material-cost-check-btn span {
    transition: color 0.2s ease, transform 0.2s ease;
}

.materials-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
    border-radius: 0.375rem;
    white-space: nowrap;
    cursor: help;
    transition: all 0.2s ease;
}

.materials-badge:hover {
    transform: scale(1.05);
}

.materials-badge.bg-success {
    background-color: #198754 !important;
    color: white;
    box-shadow: 0 2px 4px rgba(25, 135, 84, 0.3);
}

.materials-badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
}

.materials-badge.bg-danger {
    background-color: #dc3545 !important;
    color: white;
}

.labor-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.5rem;
    border-radius: 0.375rem;
    white-space: nowrap;
    cursor: help;
    transition: all 0.2s ease;
}

.labor-badge:hover {
    transform: scale(1.05);
}

.labor-badge.bg-warning {
    background-color: #fd7e14 !important;
    color: white;
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}

.labor-badge.bg-secondary {
    background-color: #6c757d !important;
    color: white;
}

.labor-badge.bg-danger {
    background-color: #dc3545 !important;
    color: white;
}

/* Mobile Card View Styles */
.mobile-card-view {
    display: none;
}

.work-order-card {
    background: var(--card-bg);
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1rem;
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
}

.work-order-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.work-order-card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    padding: 1rem;
    position: relative;
}

.work-order-card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #28a745, #20c997, #17a2b8);
}

.work-order-number {
    font-size: 1.1rem;
    font-weight: 600;
    margin: 0;
    text-decoration: none;
    color: white;
}

.work-order-number:hover {
    color: #f8f9fa;
    text-decoration: underline;
}

.work-order-card-body {
    padding: 1rem;
}

.card-field-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #f8f9fa;
}

.card-field-row:last-child {
    border-bottom: none;
}

.field-label {
    font-weight: 600;
    color: var(--text-color);
    font-size: 0.85rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

.field-value {
    font-weight: 500;
    color: var(--text-color);
    text-align: right;
    max-width: 60%;
    word-wrap: break-word;
}

.card-description {
    background: #f8f9fa;
    padding: 0.75rem;
    border-radius: 8px;
    margin: 0.5rem 0;
    border-left: 4px solid #007bff;
}

.card-actions {
    padding: 1rem;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.card-action-btn {
    flex: 1;
    min-width: 80px;
    padding: 0.5rem 0.75rem;
    border-radius: 8px;
    border: none;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.25rem;
}

.card-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.card-action-btn.btn-success {
    background: linear-gradient(135deg, #28a745, #20c997);
    color: white;
}

.card-action-btn.btn-primary {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
}

.card-action-btn.btn-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
}

/* Dark mode overrides for warning buttons */
[data-bs-theme="dark"] .card-action-btn.btn-warning {
    color: #000000 !important;
}

.card-action-btn.btn-outline-info {
    background: linear-gradient(135deg, #17a2b8, #138496);
    color: white;
    border: 1px solid #17a2b8;
}

.card-checkbox {
    position: absolute;
    top: 1rem;
    right: 1rem;
    transform: scale(1.2);
}

.materials-check-card {
    background: #e8f5e8;
    border: 1px solid #28a745;
    border-radius: 8px;
    padding: 0.5rem;
    text-align: center;
}

.materials-check-card .btn {
    background: linear-gradient(135deg, #28a745, #20c997);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.materials-check-card .btn:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
}

.labor-check-card {
    background: rgba(var(--warning-color-rgb), 0.1);
    border: 1px solid #fd7e14;
    border-radius: 8px;
    padding: 0.5rem;
    text-align: center;
}

.labor-check-card .btn {
    background: linear-gradient(135deg, #fd7e14, #ff9800);
    border: none;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.labor-check-card .btn:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(253, 126, 20, 0.3);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .materials-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.375rem;
    }

    .materials-check-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    .labor-badge {
        font-size: 0.7rem;
        padding: 0.25rem 0.375rem;
    }

    .labor-check-btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.8rem;
    }

    /* Hide table view and show card view on mobile */
    .table-responsive {
        display: none !important;
    }

    .mobile-card-view {
        display: block !important;
    }

    .work-order-card {
        margin-bottom: 1.5rem;
    }

    .card-actions {
        flex-direction: column;
    }

    .card-action-btn {
        min-width: 100%;
        margin-bottom: 0.5rem;
    }

    .card-action-btn:last-child {
        margin-bottom: 0;
    }
}

/* Enhanced Status Badge Styles with Better Contrast */
.status-badge {
    font-weight: 600;
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.status-ASSIGN {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}
.status-APPR {
    background: linear-gradient(135deg, #28a745, #1e7e34);
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}
.status-READY {
    background: linear-gradient(135deg, #20c997, #17a2b8);
    color: white;
    box-shadow: 0 2px 4px rgba(32, 201, 151, 0.3);
}
.status-INPRG {
    background: linear-gradient(135deg, #fd7e14, #e55a00);
    color: white;
    box-shadow: 0 2px 4px rgba(253, 126, 20, 0.3);
}
.status-WMATL {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    text-shadow: none;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}
.status-WAPPR {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    text-shadow: none;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}
.status-WGOVT {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    text-shadow: none;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}
.status-WSERV {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    text-shadow: none;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}
.status-WSCH {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    text-shadow: none;
    font-weight: 700;
    box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
}

/* Dark mode overrides for yellow status badges */
[data-bs-theme="dark"] .status-WMATL,
[data-bs-theme="dark"] .status-WAPPR,
[data-bs-theme="dark"] .status-WGOVT,
[data-bs-theme="dark"] .status-WSERV,
[data-bs-theme="dark"] .status-WSCH {
    color: #000000 !important;
}
.status-COMP {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}
.status-CLOSE {
    background: linear-gradient(135deg, #343a40, #23272b);
    color: white;
    box-shadow: 0 2px 4px rgba(52, 58, 64, 0.3);
}
.status-PACK {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}
.status-DEFER {
    background: linear-gradient(135deg, #6c757d, #495057);
    color: white;
    box-shadow: 0 2px 4px rgba(108, 117, 125, 0.3);
}

/* Table responsive improvements */
@media (max-width: 992px) {
    .table-responsive {
        font-size: 0.85rem;
    }

    .btn-group .btn {
        padding: 0.25rem 0.375rem;
        font-size: 0.75rem;
    }
}

/* Dark mode comprehensive support */
[data-bs-theme="dark"] .work-order-card {
    background: var(--card-bg);
    border-color: var(--border-color);
}

[data-bs-theme="dark"] .work-order-card-header {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--header-text-color);
}

[data-bs-theme="dark"] .work-order-number {
    color: var(--header-text-color);
}

[data-bs-theme="dark"] .work-order-number:hover {
    color: rgba(var(--header-text-color-rgb), 0.8);
}

[data-bs-theme="dark"] .field-label {
    color: var(--text-color);
    opacity: 0.7;
}

[data-bs-theme="dark"] .field-value {
    color: var(--text-color);
}

[data-bs-theme="dark"] .table-light {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .table-light th {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table-hover tbody tr:hover {
    background-color: rgba(var(--primary-color-rgb), 0.1) !important;
}

[data-bs-theme="dark"] .badge.bg-light {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .badge.bg-secondary {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .text-muted {
    color: rgba(var(--text-color-rgb), 0.6) !important;
}

[data-bs-theme="dark"] .text-primary {
    color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .alert-info {
    background-color: rgba(var(--primary-color-rgb), 0.1);
    border-color: var(--primary-color);
    color: var(--text-color);
}

[data-bs-theme="dark"] .alert-light {
    background-color: rgba(var(--text-color-rgb), 0.1);
    border-color: var(--border-color);
    color: var(--text-color);
}

[data-bs-theme="dark"] .card-description {
    color: var(--text-color);
}

[data-bs-theme="dark"] .card-description strong {
    color: var(--text-color);
}

[data-bs-theme="dark"] .modal-content {
    background-color: var(--card-bg);
    color: var(--text-color);
}

[data-bs-theme="dark"] .modal-header {
    border-bottom-color: var(--border-color);
}

[data-bs-theme="dark"] .modal-footer {
    border-top-color: var(--border-color);
}

[data-bs-theme="dark"] .table-dark {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
}

[data-bs-theme="dark"] .table-dark th {
    color: var(--header-text-color) !important;
    border-color: var(--border-color) !important;
}

/* Additional table text readability fixes */
[data-bs-theme="dark"] .table td {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table th {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .table tbody tr {
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .table a {
    color: var(--primary-color) !important;
}

[data-bs-theme="dark"] .table a:hover {
    color: var(--secondary-color) !important;
}

[data-bs-theme="dark"] .form-text {
    color: rgba(var(--text-color-rgb), 0.6) !important;
}

[data-bs-theme="dark"] .small {
    color: rgba(var(--text-color-rgb), 0.7) !important;
}

[data-bs-theme="dark"] .text-truncate {
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .mobile-card-view {
    background: var(--background-color);
}

[data-bs-theme="dark"] .mobile-card-view .card {
    background: var(--card-bg);
    border-color: var(--border-color);
}

/* CRITICAL: Fix all card backgrounds and headers for dark mode */
[data-bs-theme="dark"] .card {
    background-color: var(--card-bg) !important;
    border-color: var(--border-color) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .card-header {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-bottom-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .card-body {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] .card-footer {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
    border-top-color: var(--border-color) !important;
}

/* Fix specific card header classes */
[data-bs-theme="dark"] .card-header.bg-primary {
    background-color: var(--primary-color) !important;
    color: var(--header-text-color) !important;
}

[data-bs-theme="dark"] .card-header.bg-success {
    background-color: var(--success-color) !important;
    color: var(--header-text-color) !important;
}

[data-bs-theme="dark"] .card-header.bg-info {
    background-color: var(--secondary-color) !important;
    color: var(--header-text-color) !important;
}

/* Fix search results container */
[data-bs-theme="dark"] #searchResults {
    background-color: transparent !important;
}

[data-bs-theme="dark"] #searchResults .card {
    background-color: var(--card-bg) !important;
}

/* Fix outline buttons in dark mode */
[data-bs-theme="dark"] .btn-outline-light {
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .btn-outline-light:hover {
    background-color: var(--border-color) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

/* Fix any remaining white backgrounds */
[data-bs-theme="dark"] .bg-white {
    background-color: var(--card-bg) !important;
}

[data-bs-theme="dark"] .table-responsive {
    background-color: transparent !important;
}

/* Force override Bootstrap's default card styling in dark mode */
[data-bs-theme="dark"] .card,
[data-bs-theme="dark"] .card > .card-header,
[data-bs-theme="dark"] .card > .card-body,
[data-bs-theme="dark"] .card > .card-footer {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
    border-color: var(--border-color) !important;
}

[data-bs-theme="dark"] .card > .card-header {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
}

/* Ensure search results container has no white background */
[data-bs-theme="dark"] #searchResults,
[data-bs-theme="dark"] #searchResults > *,
[data-bs-theme="dark"] #searchResults .card,
[data-bs-theme="dark"] #searchResults .card-header,
[data-bs-theme="dark"] #searchResults .card-body {
    background-color: var(--card-bg) !important;
    color: var(--text-color) !important;
}

[data-bs-theme="dark"] #searchResults .card-header {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
}

/* Fix any remaining Bootstrap overrides */
[data-bs-theme="dark"] .shadow-sm {
    box-shadow: var(--box-shadow) !important;
}

[data-bs-theme="dark"] .border-0 {
    border-color: var(--border-color) !important;
}

/* Ensure page background is properly themed */
[data-bs-theme="dark"] body {
    background-color: var(--background-color) !important;
    color: var(--text-color) !important;
}

/* Fix welcome header badges */
[data-bs-theme="dark"] .welcome-header .badge.bg-warning {
    color: #000000 !important;
}

/* Fix any remaining container backgrounds */
[data-bs-theme="dark"] .container,
[data-bs-theme="dark"] .container-fluid {
    background-color: transparent !important;
}

/* Ensure all divs inherit proper background */
[data-bs-theme="dark"] div:not(.badge):not(.btn):not(.alert) {
    background-color: inherit;
    color: inherit;
}

/* NUCLEAR OPTION: Force override the damn table-light header */
[data-bs-theme="dark"] .table-light,
[data-bs-theme="dark"] thead.table-light,
[data-bs-theme="dark"] .table-light th,
[data-bs-theme="dark"] .table-light td,
[data-bs-theme="dark"] thead.table-light th,
[data-bs-theme="dark"] thead.table-light td {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-color: var(--border-color) !important;
}

/* Force override Bootstrap's table-light class completely */
[data-bs-theme="dark"] .table > thead > tr > th.table-light,
[data-bs-theme="dark"] .table > thead.table-light > tr > th {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-bottom-color: var(--border-color) !important;
}

/* Target the specific table in workorders */
[data-bs-theme="dark"] #workordersTable thead.table-light {
    background-color: var(--header-bg) !important;
}

[data-bs-theme="dark"] #workordersTable thead.table-light th {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-color: var(--border-color) !important;
}

/* Override any remaining Bootstrap table styling */
[data-bs-theme="dark"] .table thead th {
    background-color: var(--header-bg) !important;
    color: var(--header-text-color) !important;
    border-bottom: 2px solid var(--border-color) !important;
}

/* Signature Pad Styles */
.signature-pad-container {
    margin-bottom: 1rem;
}

.signature-pad-wrapper {
    position: relative;
    border: 2px solid var(--border-color);
    border-radius: var(--border-radius);
    background: #fafafa;
    overflow: hidden;
    transition: border-color 0.3s ease;
}

.signature-pad-wrapper:hover {
    border-color: var(--primary-color);
}

.signature-pad-wrapper.active {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(var(--primary-color-rgb), 0.25);
}

#signaturePad {
    display: block;
    cursor: crosshair;
    background: white;
    width: 100%;
    height: 200px;
}

.signature-pad-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    pointer-events: none;
    transition: opacity 0.3s ease;
}

.signature-pad-overlay.hidden {
    opacity: 0;
}

.signature-pad-controls {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.signature-info {
    background: rgba(var(--info-color-rgb), 0.1);
    border: 1px solid rgba(var(--info-color-rgb), 0.2);
    border-radius: var(--border-radius);
    padding: 1rem;
}

/* Mobile signature pad adjustments */
@media (max-width: 768px) {
    #signaturePad {
        height: 150px;
    }

    .signature-pad-controls {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.5rem;
    }
}
</style>
<div class="welcome-header text-center mb-4">
    <h2 class="fw-bold">
        <i class="fas fa-clipboard-list me-2 text-primary"></i>Enhanced Work Orders
    </h2>
    <div class="badge bg-warning text-dark mb-2">Test (UAT) Environment</div>
    <div class="badge bg-primary text-white mb-3">
        <i class="fas fa-tachometer-alt me-1"></i>Lightning Fast Search
    </div>
    {% if user_site_id %}
    <div class="badge bg-info text-white mb-3">
        <i class="fas fa-map-marker-alt me-1"></i>Site: {{ user_site_id }}
    </div>
    {% endif %}
</div>

<!-- Search Filters Card -->
<div class="card border-0 shadow-sm mb-4 border-start border-primary border-4">
    <div class="card-header bg-primary text-white">
        <h5 class="mb-0">
            <i class="fas fa-search me-2"></i>Search Work Orders
        </h5>
    </div>
    <div class="card-body p-4">
        <form id="searchForm">
            <!-- Compact Search Layout -->
            <div class="compact-search-layout">
                <!-- Search Input Row -->
                <div class="search-input-row">
                    <div class="search-input-group">
                        <label for="wonumFilter" class="compact-label">
                            <i class="fas fa-search"></i>
                            <span>Search Work Orders</span>
                        </label>
                        <input type="text" class="form-control search-input-expanded" id="wonumFilter" name="wonum"
                               placeholder="Enter work order number, description, or asset number..."
                               autocomplete="off">
                        <div class="search-help-text">
                            Search by work order number, description, or asset number. Supports partial matching.
                        </div>
                    </div>
                    <button type="submit" class="btn btn-primary search-btn">
                        <i class="fas fa-search"></i>
                        <span class="d-none d-sm-inline ms-1">Search</span>
                    </button>
                </div>

                <!-- Filters Grid -->
                <div class="filters-grid">
                    <div class="filter-group">
                        <label for="siteFilter" class="compact-label">
                            <i class="fas fa-building"></i>
                            <span>Sites</span>
                        </label>
                        <select class="form-select form-select-sm" id="siteFilter" name="site_ids" multiple>
                            <option value="">Loading...</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="statusFilter" class="compact-label">
                            <i class="fas fa-flag"></i>
                            <span>Status</span>
                        </label>
                        <select class="form-select form-select-sm" id="statusFilter" name="status">
                            <option value="">All Status</option>
                            <option value="APPR">Approved</option>
                            <option value="ASSIGN">Assigned</option>
                            <option value="READY">Ready</option>
                            <option value="INPRG">In Progress</option>
                            <option value="PACK">Packed</option>
                            <option value="DEFER">Deferred</option>
                            <option value="WAPPR">Waiting Approval</option>
                            <option value="WMATL">Waiting Material</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="priorityFilter" class="compact-label">
                            <i class="fas fa-exclamation-triangle"></i>
                            <span>Priority</span>
                        </label>
                        <select class="form-select form-select-sm" id="priorityFilter" name="priority">
                            <option value="">All Priority</option>
                            <option value="1">1 - Critical</option>
                            <option value="2">2 - High</option>
                            <option value="3">3 - Medium</option>
                            <option value="4">4 - Low</option>
                        </select>
                    </div>

                    <div class="filter-group">
                        <label for="woclassFilter" class="compact-label">
                            <i class="fas fa-layer-group"></i>
                            <span>Type</span>
                        </label>
                        <select class="form-select form-select-sm" id="woclassFilter" name="woclass">
                            <option value="WORKORDER">Work Orders</option>
                            <option value="ACTIVITY">Activities</option>
                            <option value="BOTH">Both</option>
                        </select>
                    </div>
                </div>

                <!-- Description Search Row -->
                <div class="description-search-row">
                    <div class="search-input-group">
                        <label for="descriptionFilter" class="compact-label">
                            <i class="fas fa-file-text"></i>
                            <span>Description Search</span>
                        </label>
                        <input type="text" class="form-control" id="descriptionFilter" name="description"
                               placeholder="Search in work order descriptions..."
                               autocomplete="off">
                    </div>
                </div>
            </div>
                </div>
            </div>
        </form>

        <div class="row mt-3">
            <div class="col-12">
                <div class="alert alert-info mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Lazy Loading:</strong> Enter search criteria above to find work orders.
                    Results are sorted by Report Date (ascending) and limited to 20 records per page for optimal performance.
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Search Results Container -->
<div id="searchResults" style="display: none;">
    <!-- Performance Metrics Card (Hidden on Mobile) -->
    <div class="card border-0 shadow-sm mb-4 border-start border-success border-4 d-none d-md-block">
        <div class="card-header bg-success text-white">
            <h5 class="mb-0">
                <i class="fas fa-chart-line me-2"></i>Search Performance
            </h5>
        </div>
        <div class="card-body p-4">
            <div class="row">
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-primary mb-1" id="searchTime">-</div>
                        <small class="text-muted">Search Time</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-success mb-1" id="resultCount">-</div>
                        <small class="text-muted">Results Found</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-info mb-1" id="currentPage">-</div>
                        <small class="text-muted">Current Page</small>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <div class="h4 text-warning mb-1" id="totalPages">-</div>
                        <small class="text-muted">Total Pages</small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Work Orders Results Table -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-header" style="background: var(--header-bg); color: var(--header-text-color);">
            <div class="d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-list me-2"></i>Search Results (<span id="workorderCount">0</span>)
                </h5>
                <div>
                    <button class="btn btn-outline-light btn-sm me-2" id="selectAllBtn">
                        <i class="fas fa-check-square me-1"></i>Select All
                    </button>
                    <button class="btn btn-outline-light btn-sm" id="clearSelectionBtn">
                        <i class="fas fa-square me-1"></i>Clear All
                    </button>
                </div>
            </div>
        </div>
        <div class="card-body p-0">
            <!-- Desktop Table View -->
            <div class="table-responsive">
                <table class="table table-hover mb-0" id="workordersTable">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAllCheckbox" class="form-check-input">
                            </th>
                            <th>Work Order</th>
                            <th>Site</th>
                            <th>Description</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Materials</th>
                            <th>Material Cost</th>
                            <th>Labor Hours</th>
                            <th>Labor Cost</th>
                            <th>Work Type</th>
                            <th>Assigned To</th>
                            <th>Location</th>
                            <th>Asset</th>
                            <th>Report Date</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody id="workordersTableBody">
                        <!-- Dynamic content will be inserted here -->
                    </tbody>
                </table>
            </div>

            <!-- Mobile Card View -->
            <div class="mobile-card-view p-3" id="workordersCardView">
                <!-- Dynamic card content will be inserted here -->
            </div>
        </div>
    </div>

    <!-- Pagination -->
    <div class="card border-0 shadow-sm mb-4">
        <div class="card-body">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <span class="text-muted">
                        Showing <span id="showingFrom">0</span> to <span id="showingTo">0</span>
                        of <span id="totalResults">0</span> results
                    </span>
                </div>
                <nav aria-label="Work order pagination">
                    <ul class="pagination mb-0" id="pagination">
                        <!-- Dynamic pagination will be inserted here -->
                    </ul>
                </nav>
            </div>
        </div>
    </div>
</div>

<!-- Initial Empty State -->
<div id="emptyState" class="card border-0 shadow-sm mb-4">
    <div class="card-body text-center p-5">
        <i class="fas fa-search fa-3x text-muted mb-3"></i>
        <h5 class="text-muted">Ready to Search</h5>
        <p class="text-muted">
            Use the search filters above to find work orders.
            Results will be displayed here with lightning-fast performance.
        </p>
        <div class="alert alert-light border">
            <small>
                <i class="fas fa-lightbulb text-warning me-1"></i>
                <strong>Tip:</strong> Select one or more sites and add filters for best results.
                You can search across multiple sites simultaneously. Use the Work Order # field for exact or partial number matching.
                All searches exclude tasks and history records.
            </small>
        </div>
    </div>
</div>

<!-- Navigation -->
<div class="text-center mt-4 mb-5">
    <a href="{{ url_for('welcome') }}" class="btn btn-outline-primary me-2">
        <i class="fas fa-arrow-left me-2"></i>Back to Welcome
    </a>
    <a href="{{ url_for('enhanced_profile') }}" class="btn btn-outline-success me-2">
        <i class="fas fa-rocket me-2"></i>Enhanced Profile
    </a>
    <a href="/api-docs" class="btn btn-outline-info me-2">
        <i class="fas fa-code me-2"></i>API Docs
    </a>
    <a href="{{ url_for('logout') }}" class="btn btn-outline-danger">
        <i class="fas fa-sign-out-alt me-2"></i>Logout
    </a>
</div>

<script>
let currentPage = 1;
let currentSearchCriteria = {};
let isSearching = false;

document.addEventListener('DOMContentLoaded', function() {
    // Load available sites first
    loadAvailableSites();

    // Initialize search form
    const searchForm = document.getElementById('searchForm');
    searchForm.addEventListener('submit', function(e) {
        e.preventDefault();
        performSearch(1); // Start from page 1
    });

    // Initialize selection handlers
    document.getElementById('selectAllBtn').addEventListener('click', selectAllWorkOrders);
    document.getElementById('clearSelectionBtn').addEventListener('click', clearAllSelections);
    document.getElementById('selectAllCheckbox').addEventListener('change', toggleAllWorkOrders);

    // Check for URL parameters and auto-populate search form
    handleUrlParameters();
});

function handleUrlParameters() {
    const urlParams = new URLSearchParams(window.location.search);
    const wonum = urlParams.get('wonum');
    const assetnum = urlParams.get('assetnum');
    const siteid = urlParams.get('siteid');
    const autoSearch = urlParams.get('auto_search');

    if (wonum || assetnum || siteid) {
        console.log(`🔗 URL Parameters detected - WO#: ${wonum}, Asset#: ${assetnum}, Site: ${siteid}, Auto-search: ${autoSearch}`);

        // Populate search field with work order number or asset number
        const searchField = document.getElementById('wonumFilter');
        if (searchField) {
            if (wonum) {
                searchField.value = wonum;
                console.log(`📝 Pre-filled work order number: ${wonum}`);
            } else if (assetnum) {
                searchField.value = assetnum;
                console.log(`📝 Pre-filled asset number: ${assetnum}`);
            }
        }

        // Pre-select the site if provided
        if (siteid) {
            // Wait for sites to load, then select the appropriate site
            const checkSitesLoaded = setInterval(() => {
                const siteFilter = document.getElementById('siteFilter');
                if (siteFilter && siteFilter.options.length > 1) { // More than just "Loading..."
                    // Find and select the matching site option
                    for (let option of siteFilter.options) {
                        if (option.value === siteid) {
                            option.selected = true;
                            console.log(`🏢 Pre-selected site: ${siteid}`);
                            break;
                        }
                    }
                    clearInterval(checkSitesLoaded);

                    // Auto-perform search if requested
                    if (autoSearch === 'true') {
                        console.log('🔍 Auto-performing search...');
                        setTimeout(() => performSearch(1), 500); // Small delay to ensure form is ready
                    }
                }
            }, 100);

            // Clear the interval after 10 seconds to prevent infinite checking
            setTimeout(() => clearInterval(checkSitesLoaded), 10000);
        } else if (autoSearch === 'true') {
            // Auto-search even without site pre-selection
            setTimeout(() => performSearch(1), 1000);
        }
    }
}

async function loadAvailableSites() {
    try {
        const response = await fetch('/api/enhanced-workorders/available-sites');
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        if (result.success) {
            populateSiteDropdown(result.sites, result.default_site);
        } else {
            console.error('Failed to load sites:', result.error);
            showSiteLoadError();
        }
    } catch (error) {
        console.error('Error loading available sites:', error);
        showSiteLoadError();
    }
}

function populateSiteDropdown(sites, defaultSite) {
    const siteFilter = document.getElementById('siteFilter');

    // Clear existing options
    siteFilter.innerHTML = '';

    if (sites.length === 0) {
        siteFilter.innerHTML = '<option value="">No sites available</option>';
        return;
    }

    // Add sites as options
    sites.forEach(site => {
        const option = document.createElement('option');
        option.value = site.siteid;
        option.textContent = `${site.siteid}${site.description !== site.siteid ? ' - ' + site.description : ''}`;

        // Pre-select the user's default site
        if (site.siteid === defaultSite) {
            option.selected = true;
        }

        siteFilter.appendChild(option);
    });

    console.log(`✅ Loaded ${sites.length} available sites, default: ${defaultSite}`);
}

function showSiteLoadError() {
    const siteFilter = document.getElementById('siteFilter');
    siteFilter.innerHTML = '<option value="">Error loading sites</option>';
}

async function performSearch(page = 1) {
    if (isSearching) return;

    isSearching = true;
    currentPage = page;

    // Get search criteria from form
    const formData = new FormData(document.getElementById('searchForm'));
    currentSearchCriteria = {};

    for (let [key, value] of formData.entries()) {
        if (value.trim()) {
            if (key === 'site_ids') {
                // Handle multiple site selection
                if (!currentSearchCriteria[key]) {
                    currentSearchCriteria[key] = [];
                }
                currentSearchCriteria[key].push(value.trim());
            } else {
                currentSearchCriteria[key] = value.trim();
            }
        }
    }

    // Log selected sites for debugging
    if (currentSearchCriteria.site_ids) {
        console.log(`🏢 Selected sites: ${currentSearchCriteria.site_ids.join(', ')}`);
    }

    // Show loading state
    showLoadingState();

    try {
        const startTime = Date.now();

        const response = await fetch('/api/enhanced-workorders/search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                search_criteria: currentSearchCriteria,
                page: page,
                page_size: 20
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        const result = await response.json();
        const searchTime = (Date.now() - startTime) / 1000;

        displaySearchResults(result, searchTime);

    } catch (error) {
        console.error('Search error:', error);
        showErrorState(error.message);
    } finally {
        isSearching = false;
    }
}

function showLoadingState() {
    // Hide empty state
    document.getElementById('emptyState').style.display = 'none';

    // Show search results container
    const searchResults = document.getElementById('searchResults');
    searchResults.style.display = 'block';

    // Show loading in table
    const tableBody = document.getElementById('workordersTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="13" class="text-center p-4">
                <div class="spinner-border text-primary me-2" role="status"></div>
                Searching work orders...
            </td>
        </tr>
    `;

    // Update metrics with loading state
    document.getElementById('searchTime').textContent = '...';
    document.getElementById('resultCount').textContent = '...';
    document.getElementById('currentPage').textContent = '...';
    document.getElementById('totalPages').textContent = '...';
    document.getElementById('workorderCount').textContent = '...';
}

function displaySearchResults(result, searchTime) {
    // Cache DOM elements for better performance
    const searchTimeEl = document.getElementById('searchTime');
    const resultCountEl = document.getElementById('resultCount');
    const currentPageEl = document.getElementById('currentPage');
    const totalPagesEl = document.getElementById('totalPages');

    // Update performance metrics
    if (searchTimeEl) searchTimeEl.textContent = searchTime.toFixed(3) + 's';
    if (resultCountEl) resultCountEl.textContent = result.workorders.length;
    if (currentPageEl) currentPageEl.textContent = result.page;
    if (totalPagesEl) totalPagesEl.textContent = result.total_pages;
    document.getElementById('workorderCount').textContent = result.workorders.length;

    // Update pagination info
    const showingFrom = result.workorders.length > 0 ? ((result.page - 1) * result.page_size) + 1 : 0;
    const showingTo = Math.min(result.page * result.page_size, result.total_count);
    document.getElementById('showingFrom').textContent = showingFrom;
    document.getElementById('showingTo').textContent = showingTo;
    document.getElementById('totalResults').textContent = result.total_count;

    // Populate table
    populateWorkOrdersTable(result.workorders);

    // Update pagination
    updatePagination(result);

    // Show results
    document.getElementById('searchResults').style.display = 'block';
    document.getElementById('emptyState').style.display = 'none';
}

function populateWorkOrdersTable(workorders) {
    const tableBody = document.getElementById('workordersTableBody');
    const cardView = document.getElementById('workordersCardView');

    if (workorders.length === 0) {
        tableBody.innerHTML = `
            <tr>
                <td colspan="13" class="text-center p-4 text-muted">
                    <i class="fas fa-search me-2"></i>No work orders found matching your search criteria.
                </td>
            </tr>
        `;
        cardView.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-search me-2"></i>No work orders found matching your search criteria.</div>';
        return;
    }

    // Populate table view (desktop) - Optimized for performance
    const fragment = document.createDocumentFragment();
    const tableRows = workorders.map(wo => `
        <tr>
            <td>
                <input type="checkbox" class="form-check-input work-order-checkbox"
                       value="${wo.wonum}" onchange="updateSelectedCount()">
            </td>
            <td>
                <a href="/enhanced-workorder-details/${wo.wonum}" class="text-decoration-none">
                    <strong class="text-primary">${wo.wonum}</strong>
                </a>
            </td>
            <td>
                <span class="badge bg-secondary">${wo.siteid || '-'}</span>
            </td>
            <td>
                <div class="text-truncate" style="max-width: 200px;" title="${wo.description || ''}">
                    ${wo.description || '-'}
                </div>
            </td>
            <td>${getStatusBadge(wo.status)}</td>
            <td>${getPriorityBadge(wo.priority)}</td>
            <td>
                <div id="materials-${wo.wonum}" class="materials-check-container">
                    <button class="btn btn-sm btn-outline-secondary materials-check-btn"
                            onclick="checkMaterials('${wo.wonum}', '${wo.siteid}')"
                            title="Check for planned materials">
                        <i class="fas fa-boxes"></i>
                        <span class="d-none d-md-inline ms-1">Check Materials</span>
                    </button>
                </div>
            </td>
            <td>
                <div id="material-cost-${wo.wonum}" class="material-cost-check-container">
                    <button class="btn btn-sm btn-outline-secondary material-cost-check-btn"
                            onclick="checkMaterialCost('${wo.wonum}', '${wo.siteid}')"
                            title="Check material cost">
                        <i class="fas fa-dollar-sign"></i>
                        <span class="d-none d-md-inline ms-1">Material Cost</span>
                    </button>
                </div>
            </td>
            <td>
                <div id="labor-${wo.wonum}" class="labor-check-container">
                    <button class="btn btn-sm btn-outline-secondary labor-check-btn"
                            onclick="checkLaborHours('${wo.wonum}', '${wo.siteid}')"
                            title="Check for labor hours">
                        <i class="fas fa-user-clock"></i>
                        <span class="d-none d-md-inline ms-1">Check Labor</span>
                    </button>
                </div>
            </td>
            <td>
                <div id="labor-cost-${wo.wonum}" class="labor-cost-check-container">
                    <button class="btn btn-sm btn-outline-secondary labor-cost-check-btn"
                            onclick="checkLaborCost('${wo.wonum}', '${wo.siteid}')"
                            title="Check labor cost">
                        <i class="fas fa-money-bill-wave"></i>
                        <span class="d-none d-md-inline ms-1">Labor Cost</span>
                    </button>
                </div>
            </td>
            <td>${wo.worktype || '-'}</td>
            <td>${wo.assignedto || '-'}</td>
            <td>${wo.location || '-'}</td>
            <td>${wo.assetnum || '-'}</td>
            <td>
                ${wo.reportdate ? `<small>${wo.reportdate.substring(0, 10)}</small>` : '<span class="text-muted">-</span>'}
            </td>
            <td>
                <div class="btn-group" role="group">
                    <button class="btn btn-sm btn-success" onclick="executeIndividualMethod('approve', '${wo.wonum}')" title="Approve">
                        <i class="fas fa-check"></i>
                    </button>
                    <button class="btn btn-sm btn-primary" onclick="executeIndividualMethod('start', '${wo.wonum}')" title="Start">
                        <i class="fas fa-play"></i>
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="executeIndividualMethod('complete', '${wo.wonum}')" title="Complete">
                        <i class="fas fa-flag-checkered"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="showTaskDetails('${wo.wonum}')" title="View Task Details">
                        <i class="fas fa-tasks"></i>
                    </button>
                    <a href="/enhanced-workorder-details/${wo.wonum}" class="btn btn-sm btn-outline-info workorder-detail-link" title="View Details">
                        <i class="fas fa-eye"></i>
                    </a>
                </div>
            </td>
        </tr>
    `).join('');

    // Use efficient DOM update
    tableBody.innerHTML = tableRows;

    // Store work orders for mobile navigation
    window.mobileWorkOrders = workorders;
    window.currentMobileWOIndex = 0;

    // Store work orders for detail page navigation
    storeWorkOrderListForNavigation(workorders);

    // Create single card view with navigation for mobile
    renderMobileSingleWorkOrderCard();

    // Reset selection count
    updateSelectedCount();
}

function renderMobileSingleWorkOrderCard() {
    const cardView = document.getElementById('workordersCardView');
    if (!cardView || !window.mobileWorkOrders || window.mobileWorkOrders.length === 0) return;

    const wo = window.mobileWorkOrders[window.currentMobileWOIndex];
    const totalWorkOrders = window.mobileWorkOrders.length;
    const currentPosition = window.currentMobileWOIndex + 1;

    cardView.innerHTML = `
        <div class="mobile-single-card-container">
            <!-- Navigation Header -->
            <div class="mobile-nav-header">
                <div class="mobile-nav-counter">
                    <span class="current-position">${currentPosition}</span>
                    <span class="separator">of</span>
                    <span class="total-assets">${totalWorkOrders}</span>
                </div>
                <div class="mobile-nav-buttons">
                    <button class="btn btn-outline-secondary btn-sm" onclick="navigateMobileWorkOrder(-1)" ${window.currentMobileWOIndex === 0 ? 'disabled' : ''}>
                        <i class="fas fa-chevron-left"></i>
                    </button>
                    <button class="btn btn-outline-secondary btn-sm" onclick="navigateMobileWorkOrder(1)" ${window.currentMobileWOIndex === totalWorkOrders - 1 ? 'disabled' : ''}>
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>

            <!-- Work Order Card -->
            <div class="mobile-work-order-card">
                <div class="mobile-work-order-header">
                    <div class="mobile-work-order-icon">
                        <i class="fas fa-clipboard-list"></i>
                    </div>
                    <div class="mobile-work-order-info">
                        <!-- Work Order Number with Label -->
                        <div class="mobile-header-field">
                            <div class="mobile-header-label">
                                <i class="fas fa-hashtag"></i>
                                <span>Work Order #</span>
                            </div>
                            <h5 class="mobile-work-order-title">${wo.wonum || 'Unknown'}</h5>
                        </div>

                        <!-- Description with Label -->
                        ${wo.description ? `
                        <div class="mobile-header-field">
                            <div class="mobile-header-label">
                                <i class="fas fa-file-text"></i>
                                <span>Description</span>
                            </div>
                            <p class="mobile-work-order-description">${wo.description}</p>
                        </div>
                        ` : ''}

                        <!-- Site and Status with Labels -->
                        <div class="mobile-header-badges">
                            <div class="mobile-badge-item">
                                <div class="mobile-badge-label">
                                    <i class="fas fa-building"></i>
                                    <span>Site</span>
                                </div>
                                <span class="badge bg-secondary">${wo.siteid || '-'}</span>
                            </div>
                            <div class="mobile-badge-item">
                                <div class="mobile-badge-label">
                                    <i class="fas fa-circle"></i>
                                    <span>Status</span>
                                </div>
                                ${getStatusBadge(wo.status)}
                            </div>
                            <div class="mobile-badge-item">
                                <div class="mobile-badge-label">
                                    <i class="fas fa-exclamation-triangle"></i>
                                    <span>Priority</span>
                                </div>
                                ${getPriorityBadge(wo.priority)}
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Complete Work Order Information -->
                <div class="mobile-work-order-details">
                    <div class="mobile-detail-row">
                        <div class="mobile-detail-item">
                            <i class="fas fa-tools"></i>
                            <div class="mobile-detail-content">
                                <span class="mobile-detail-label">Work Type</span>
                                <span class="mobile-detail-value">${wo.worktype || '-'}</span>
                            </div>
                        </div>
                        <div class="mobile-detail-item">
                            <i class="fas fa-user"></i>
                            <div class="mobile-detail-content">
                                <span class="mobile-detail-label">Assigned To</span>
                                <span class="mobile-detail-value">${wo.assignedto || '-'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="mobile-detail-row">
                        <div class="mobile-detail-item">
                            <i class="fas fa-map-pin"></i>
                            <div class="mobile-detail-content">
                                <span class="mobile-detail-label">Location</span>
                                <span class="mobile-detail-value">${wo.location || '-'}</span>
                            </div>
                        </div>
                        <div class="mobile-detail-item">
                            <i class="fas fa-cog"></i>
                            <div class="mobile-detail-content">
                                <span class="mobile-detail-label">Asset</span>
                                <span class="mobile-detail-value">${wo.assetnum || '-'}</span>
                            </div>
                        </div>
                    </div>

                    <div class="mobile-detail-row">
                        <div class="mobile-detail-item">
                            <i class="fas fa-calendar"></i>
                            <div class="mobile-detail-content">
                                <span class="mobile-detail-label">Report Date</span>
                                <span class="mobile-detail-value">${wo.reportdate ? wo.reportdate.substring(0, 10) : '-'}</span>
                            </div>
                        </div>
                        <div class="mobile-detail-item">
                            <i class="fas fa-calendar-check"></i>
                            <div class="mobile-detail-content">
                                <span class="mobile-detail-label">Target Date</span>
                                <span class="mobile-detail-value">${wo.targstartdate ? wo.targstartdate.substring(0, 10) : '-'}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Check Buttons -->
                <div class="mobile-check-buttons">
                    <button class="btn btn-outline-success mobile-check-btn" onclick="checkMaterials('${wo.wonum}', '${wo.siteid}')" title="Check Materials" id="mobile-materials-${wo.wonum}">
                        <i class="fas fa-boxes me-1"></i>Materials
                    </button>
                    <button class="btn btn-outline-info mobile-check-btn" onclick="checkMaterialCost('${wo.wonum}', '${wo.siteid}')" title="Check Material Cost" id="mobile-material-cost-${wo.wonum}">
                        <i class="fas fa-dollar-sign me-1"></i>Mat Cost
                    </button>
                    <button class="btn btn-outline-warning mobile-check-btn" onclick="checkLaborHours('${wo.wonum}', '${wo.siteid}')" title="Check Labor" id="mobile-labor-${wo.wonum}">
                        <i class="fas fa-user-clock me-1"></i>Labor
                    </button>
                    <button class="btn btn-outline-secondary mobile-check-btn" onclick="checkLaborCost('${wo.wonum}', '${wo.siteid}')" title="Check Labor Cost" id="mobile-labor-cost-${wo.wonum}">
                        <i class="fas fa-money-bill-wave me-1"></i>Lab Cost
                    </button>
                </div>

                <!-- Action Buttons -->
                <div class="mobile-action-buttons">
                    <button class="btn btn-success mobile-detail-btn" onclick="executeIndividualMethod('approve', '${wo.wonum}')" title="Approve">
                        <i class="fas fa-check me-1"></i>Approve
                    </button>
                    <button class="btn btn-primary mobile-detail-btn" onclick="executeIndividualMethod('start', '${wo.wonum}')" title="Start">
                        <i class="fas fa-play me-1"></i>Start
                    </button>
                    <button class="btn btn-warning mobile-detail-btn" onclick="executeIndividualMethod('complete', '${wo.wonum}')" title="Complete">
                        <i class="fas fa-flag-checkered me-1"></i>Complete
                    </button>
                    <a href="/enhanced-workorder-details/${wo.wonum}" class="btn btn-outline-info mobile-detail-btn" title="Details">
                        <i class="fas fa-eye me-1"></i>Details
                    </a>
                </div>
            </div>
        </div>
    `;
}

function navigateMobileWorkOrder(direction) {
    if (!window.mobileWorkOrders) return;

    const newIndex = window.currentMobileWOIndex + direction;
    if (newIndex >= 0 && newIndex < window.mobileWorkOrders.length) {
        // Add smooth transition
        const cardContainer = document.querySelector('.mobile-work-order-card');
        if (cardContainer) {
            cardContainer.style.opacity = '0.7';
            cardContainer.style.transform = 'translateX(' + (direction > 0 ? '10px' : '-10px') + ')';

            setTimeout(() => {
                window.currentMobileWOIndex = newIndex;
                renderMobileSingleWorkOrderCard();
            }, 150);
        } else {
            window.currentMobileWOIndex = newIndex;
            renderMobileSingleWorkOrderCard();
        }
    }
}

function getStatusBadge(status) {
    if (!status) return '<span class="text-muted">-</span>';

    // Use the enhanced status badge classes
    return `<span class="badge status-badge status-${status}">${status}</span>`;
}

function getPriorityBadge(priority) {
    if (!priority) return '<span class="text-muted">-</span>';

    const priorityNum = parseInt(priority);
    let badgeClass = 'bg-success';

    if (priorityNum <= 2) badgeClass = 'bg-danger';
    else if (priorityNum <= 3) badgeClass = 'bg-warning';

    return `<span class="badge ${badgeClass}">${priority}</span>`;
}

function updatePagination(result) {
    const pagination = document.getElementById('pagination');

    if (result.total_pages <= 1) {
        pagination.innerHTML = '';
        return;
    }

    let paginationHTML = '';

    // Previous button
    if (result.has_prev) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="performSearch(${result.page - 1})">Previous</a>
            </li>
        `;
    }

    // Page numbers
    const startPage = Math.max(1, result.page - 2);
    const endPage = Math.min(result.total_pages, result.page + 2);

    for (let i = startPage; i <= endPage; i++) {
        const isActive = i === result.page;
        paginationHTML += `
            <li class="page-item ${isActive ? 'active' : ''}">
                <a class="page-link" href="#" onclick="performSearch(${i})">${i}</a>
            </li>
        `;
    }

    // Next button
    if (result.has_next) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="performSearch(${result.page + 1})">Next</a>
            </li>
        `;
    }

    pagination.innerHTML = paginationHTML;
}

function showErrorState(errorMessage) {
    const tableBody = document.getElementById('workordersTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="13" class="text-center p-4 text-danger">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Error: ${errorMessage}
            </td>
        </tr>
    `;
}

// Selection management functions
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('.work-order-checkbox:checked');
    const count = checkboxes.length;

    // Update select all checkbox state
    const allCheckboxes = document.querySelectorAll('.work-order-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    if (count === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (count === allCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

function toggleAllWorkOrders() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const checkboxes = document.querySelectorAll('.work-order-checkbox');

    checkboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelectedCount();
}

function selectAllWorkOrders() {
    const checkboxes = document.querySelectorAll('.work-order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelectedCount();
}

function clearAllSelections() {
    const checkboxes = document.querySelectorAll('.work-order-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelectedCount();
}

// Materials availability checking
async function checkMaterials(wonum, siteid) {
    // Prevent multiple rapid clicks
    if (window.checkingMaterials && window.checkingMaterials[wonum]) return;
    if (!window.checkingMaterials) window.checkingMaterials = {};
    window.checkingMaterials[wonum] = true;

    const container = document.getElementById(`materials-${wonum}`);
    const cardContainer = document.getElementById(`materials-card-${wonum}`);
    const mobileContainer = document.getElementById(`mobile-materials-${wonum}`);
    const button = container ? container.querySelector('.materials-check-btn') : null;
    const cardButton = cardContainer ? cardContainer.querySelector('.materials-check-btn') : null;
    const mobileButton = mobileContainer;

    // Show loading state for all views
    if (button) {
        button.disabled = true;
        button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    }
    if (cardButton) {
        cardButton.disabled = true;
        cardButton.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading...';
    }
    updateMobileButtonState(mobileButton, 'loading');

    try {
        const startTime = Date.now();
        const response = await fetch(`/api/workorder/${wonum}/materials-availability`);
        const result = await response.json();
        const checkTime = (Date.now() - startTime) / 1000;

        if (result.success) {
            const availability = result.availability;

            if (availability.has_materials) {
                // Show green badge with count for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-success materials-badge" title="Found ${availability.total_materials} materials across ${availability.tasks_with_materials} tasks${availability.cache_hit ? ' (cached)' : ''}">
                            <i class="fas fa-boxes me-1"></i>
                            ${availability.total_materials} Material <br> Request${availability.total_materials !== 1 ? 's' : ''}
                        </span>
                    `;
                }

                // Show success state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-success mb-0 p-2 text-center">
                            <i class="fas fa-boxes me-2"></i>
                            <strong>${availability.total_materials} Material Request${availability.total_materials !== 1 ? 's' : ''}</strong>
                            <br><small>Found across ${availability.tasks_with_materials} task${availability.tasks_with_materials !== 1 ? 's' : ''}</small>
                        </div>
                    `;
                }

                // Show success state for mobile view
                updateMobileButtonState(mobileButton, 'success', {
                    count: availability.total_materials,
                    title: `Found ${availability.total_materials} materials across ${availability.tasks_with_materials} tasks`
                });
            } else {
                // Show gray badge indicating no materials for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-secondary materials-badge" title="No planned materials found${availability.cache_hit ? ' (cached)' : ''}">
                            <i class="fas fa-box-open me-1"></i>
                            No Materials
                        </span>
                    `;
                }

                // Show no materials state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-secondary mb-0 p-2 text-center">
                            <i class="fas fa-box-open me-2"></i>
                            <strong>No Materials</strong>
                            <br><small>No planned materials found</small>
                        </div>
                    `;
                }

                // Show no materials state for mobile view
                updateMobileButtonState(mobileButton, 'none', {
                    title: 'No planned materials found'
                });
            }

            console.log(`📦 Materials check for WO ${wonum}: ${availability.total_materials} materials in ${checkTime.toFixed(3)}s${availability.cache_hit ? ' (cached)' : ''}`);

        } else {
            // Show error state for table view
            if (container) {
                container.innerHTML = `
                    <span class="badge bg-danger materials-badge" title="Error checking materials: ${result.error}">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Error
                    </span>
                `;
            }

            // Show error state for card view
            if (cardContainer) {
                cardContainer.innerHTML = `
                    <div class="alert alert-danger mb-0 p-2 text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error</strong>
                        <br><small>${result.error}</small>
                    </div>
                `;
            }

            // Show error state for mobile view
            updateMobileButtonState(mobileButton, 'error', {
                title: `Error checking materials: ${result.error}`
            });
        }

    } catch (error) {
        console.error('Error checking materials:', error);

        // Show error state for table view
        if (container) {
            container.innerHTML = `
                <span class="badge bg-danger materials-badge" title="Network error: ${error.message}">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Error
                </span>
            `;
        }

        // Show error state for card view
        if (cardContainer) {
            cardContainer.innerHTML = `
                <div class="alert alert-danger mb-0 p-2 text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Network Error</strong>
                    <br><small>${error.message}</small>
                </div>
            `;
        }

        // Show error state for mobile view
        updateMobileButtonState(mobileButton, 'error', {
            title: `Network error: ${error.message}`
        });
    } finally {
        // Clean up the checking flag
        if (window.checkingMaterials) {
            delete window.checkingMaterials[wonum];
        }
    }
}

// Labor hours availability checking
async function checkLaborHours(wonum, siteid) {
    // Prevent multiple rapid clicks
    if (window.checkingLabor && window.checkingLabor[wonum]) return;
    if (!window.checkingLabor) window.checkingLabor = {};
    window.checkingLabor[wonum] = true;

    const container = document.getElementById(`labor-${wonum}`);
    const cardContainer = document.getElementById(`labor-card-${wonum}`);
    const mobileContainer = document.getElementById(`mobile-labor-${wonum}`);
    const button = container ? container.querySelector('.labor-check-btn') : null;
    const cardButton = cardContainer ? cardContainer.querySelector('.labor-check-btn') : null;
    const mobileButton = mobileContainer;

    // Show loading state for all views
    if (button) {
        button.disabled = true;
        button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    }
    if (cardButton) {
        cardButton.disabled = true;
        cardButton.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading...';
    }
    updateMobileButtonState(mobileButton, 'loading');

    try {
        const startTime = Date.now();
        const response = await fetch(`/api/workorder/${wonum}/labor-hours-availability`);
        const result = await response.json();
        const checkTime = (Date.now() - startTime) / 1000;

        if (result.success) {
            const availability = result.availability;

            if (availability.has_labor_hours) {
                // Show orange badge with count for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-warning labor-badge" title="Found ${availability.total_labor_records} labor records (${availability.total_regular_hours}h) across ${availability.tasks_with_labor} tasks${availability.cache_hit ? ' (cached)' : ''}">
                            <i class="fas fa-user-clock me-1"></i>
                            ${availability.total_regular_hours}h <br> (${availability.total_labor_records} records)
                        </span>
                    `;
                }

                // Show success state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-warning mb-0 p-2 text-center">
                            <i class="fas fa-user-clock me-2"></i>
                            <strong>${availability.total_regular_hours} Hours</strong>
                            <br><small>${availability.total_labor_records} labor records across ${availability.tasks_with_labor} task${availability.tasks_with_labor !== 1 ? 's' : ''}</small>
                        </div>
                    `;
                }

                // Show success state for mobile view
                updateMobileButtonState(mobileButton, 'success', {
                    count: availability.total_regular_hours,
                    title: `${availability.total_labor_records} labor records, ${availability.total_regular_hours} hours across ${availability.tasks_with_labor} tasks`
                });
            } else {
                // Show gray badge indicating no labor hours for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-secondary labor-badge" title="No labor hours found${availability.cache_hit ? ' (cached)' : ''}">
                            <i class="fas fa-user-slash me-1"></i>
                            No Labor
                        </span>
                    `;
                }

                // Show no labor state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-secondary mb-0 p-2 text-center">
                            <i class="fas fa-user-slash me-2"></i>
                            <strong>No Labor Hours</strong>
                            <br><small>No labor records found</small>
                        </div>
                    `;
                }

                // Show no labor state for mobile view
                updateMobileButtonState(mobileButton, 'none', {
                    title: 'No labor records found'
                });
            }

            console.log(`👷 Labor check for WO ${wonum}: ${availability.total_labor_records} records, ${availability.total_regular_hours}h in ${checkTime.toFixed(3)}s${availability.cache_hit ? ' (cached)' : ''}`);

        } else {
            // Show error state for table view
            if (container) {
                container.innerHTML = `
                    <span class="badge bg-danger labor-badge" title="Error checking labor hours: ${result.error}">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Error
                    </span>
                `;
            }

            // Show error state for card view
            if (cardContainer) {
                cardContainer.innerHTML = `
                    <div class="alert alert-danger mb-0 p-2 text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error</strong>
                        <br><small>${result.error}</small>
                    </div>
                `;
            }

            // Show error state for mobile view
            updateMobileButtonState(mobileButton, 'error', {
                title: `Error checking labor hours: ${result.error}`
            });

            console.error('Error checking labor hours:', result.error);
        }

    } catch (error) {
        console.error('Error checking labor hours:', error);

        // Show error state for table view
        if (container) {
            container.innerHTML = `
                <span class="badge bg-danger labor-badge" title="Network error: ${error.message}">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Error
                </span>
            `;
        }

        // Show error state for card view
        if (cardContainer) {
            cardContainer.innerHTML = `
                <div class="alert alert-danger mb-0 p-2 text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Network Error</strong>
                    <br><small>${error.message}</small>
                </div>
            `;
        }

        // Show error state for mobile view
        updateMobileButtonState(mobileButton, 'error', {
            title: `Network error: ${error.message}`
        });
    } finally {
        // Clean up the checking flag
        if (window.checkingLabor) {
            delete window.checkingLabor[wonum];
        }
    }
}

// Labor cost availability checking
async function checkLaborCost(wonum, siteid) {
    // Prevent multiple rapid clicks
    if (window.checkingLaborCost && window.checkingLaborCost[wonum]) return;
    if (!window.checkingLaborCost) window.checkingLaborCost = {};
    window.checkingLaborCost[wonum] = true;

    const container = document.getElementById(`labor-cost-${wonum}`);
    const cardContainer = document.getElementById(`labor-cost-card-${wonum}`);
    const mobileContainer = document.getElementById(`mobile-labor-cost-${wonum}`);
    const button = container ? container.querySelector('.labor-cost-check-btn') : null;
    const cardButton = cardContainer ? cardContainer.querySelector('.labor-cost-check-btn') : null;
    const mobileButton = mobileContainer;

    // Show loading state for all views
    if (button) {
        button.disabled = true;
        button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    }
    if (cardButton) {
        cardButton.disabled = true;
        cardButton.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading...';
    }
    updateMobileButtonState(mobileButton, 'loading');

    try {
        const startTime = Date.now();
        const response = await fetch(`/api/workorder/${wonum}/labor-cost-availability`);
        const result = await response.json();
        const checkTime = (Date.now() - startTime) / 1000;

        if (result.success) {
            const availability = result.availability;

            if (availability.has_labor_cost) {
                // Show orange badge with cost for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-warning labor-cost-badge" title="Found ${availability.total_labor_records} labor records ($${availability.total_labor_cost.toFixed(2)}) across ${availability.tasks_with_labor} tasks${availability.cache_hit ? ' (cached)' : ''}">
                            <i class="fas fa-money-bill-wave me-1"></i>
                            $${availability.total_labor_cost.toFixed(2)} <br> (${availability.total_labor_records} records)
                        </span>
                    `;
                }

                // Show success state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-warning mb-0 p-2 text-center">
                            <i class="fas fa-money-bill-wave me-2"></i>
                            <strong>$${availability.total_labor_cost.toFixed(2)} Labor Cost</strong>
                            <br><small>${availability.total_labor_records} records across ${availability.tasks_with_labor} tasks</small>
                        </div>
                    `;
                }

                // Show success state for mobile view
                updateMobileButtonState(mobileButton, 'cost', {
                    amount: availability.total_labor_cost.toFixed(2),
                    title: `$${availability.total_labor_cost.toFixed(2)} labor cost from ${availability.total_labor_records} records across ${availability.tasks_with_labor} tasks`
                });
            } else {
                // Show no labor cost state for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-secondary labor-cost-badge" title="No labor cost found">
                            <i class="fas fa-money-bill-slash me-1"></i>
                            $0.00
                        </span>
                    `;
                }

                // Show no labor cost state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-secondary mb-0 p-2 text-center">
                            <i class="fas fa-money-bill-slash me-2"></i>
                            <strong>No Labor Cost</strong>
                            <br><small>No labor cost found</small>
                        </div>
                    `;
                }

                // Show no labor cost state for mobile view
                updateMobileButtonState(mobileButton, 'cost', {
                    amount: '0.00',
                    title: 'No labor cost found'
                });
            }

            console.log(`💰 Labor cost check for WO ${wonum}: ${availability.total_labor_records} records, $${availability.total_labor_cost.toFixed(2)} in ${checkTime.toFixed(3)}s${availability.cache_hit ? ' (cached)' : ''}`);

        } else {
            // Show error state for table view
            if (container) {
                container.innerHTML = `
                    <span class="badge bg-danger labor-cost-badge" title="Error checking labor cost: ${result.error}">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Error
                    </span>
                `;
            }

            // Show error state for card view
            if (cardContainer) {
                cardContainer.innerHTML = `
                    <div class="alert alert-danger mb-0 p-2 text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error</strong>
                        <br><small>${result.error}</small>
                    </div>
                `;
            }

            // Show error state for mobile view
            updateMobileButtonState(mobileButton, 'error', {
                title: `Error checking labor cost: ${result.error}`
            });

            console.error('Error checking labor cost:', result.error);
        }

    } catch (error) {
        console.error('Error checking labor cost:', error);

        // Show error state for table view
        if (container) {
            container.innerHTML = `
                <span class="badge bg-danger labor-cost-badge" title="Network error: ${error.message}">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Error
                </span>
            `;
        }

        // Show error state for card view
        if (cardContainer) {
            cardContainer.innerHTML = `
                <div class="alert alert-danger mb-0 p-2 text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Network Error</strong>
                    <br><small>${error.message}</small>
                </div>
            `;
        }

        // Show error state for mobile view
        updateMobileButtonState(mobileButton, 'error', {
            title: `Network error: ${error.message}`
        });
    } finally {
        // Clean up the checking flag
        if (window.checkingLaborCost) {
            delete window.checkingLaborCost[wonum];
        }
    }
}

// Material cost availability checking
async function checkMaterialCost(wonum, siteid) {
    // Prevent multiple rapid clicks
    if (window.checkingMaterialCost && window.checkingMaterialCost[wonum]) return;
    if (!window.checkingMaterialCost) window.checkingMaterialCost = {};
    window.checkingMaterialCost[wonum] = true;

    const container = document.getElementById(`material-cost-${wonum}`);
    const cardContainer = document.getElementById(`material-cost-card-${wonum}`);
    const mobileContainer = document.getElementById(`mobile-material-cost-${wonum}`);
    const button = container ? container.querySelector('.material-cost-check-btn') : null;
    const cardButton = cardContainer ? cardContainer.querySelector('.material-cost-check-btn') : null;
    const mobileButton = mobileContainer;

    // Show loading state for all views
    if (button) {
        button.disabled = true;
        button.innerHTML = '<div class="spinner-border spinner-border-sm" role="status"></div>';
    }
    if (cardButton) {
        cardButton.disabled = true;
        cardButton.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading...';
    }
    updateMobileButtonState(mobileButton, 'loading');

    try {
        const startTime = Date.now();
        const response = await fetch(`/api/workorder/${wonum}/material-cost-availability`);
        const result = await response.json();
        const checkTime = (Date.now() - startTime) / 1000;

        if (result.success) {
            const availability = result.availability;

            if (availability.has_material_cost) {
                // Show green badge with cost for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-success material-cost-badge" title="Found ${availability.total_materials} materials ($${availability.total_material_cost.toFixed(2)}) across ${availability.tasks_with_materials} tasks${availability.cache_hit ? ' (cached)' : ''}">
                            <i class="fas fa-dollar-sign me-1"></i>
                            $${availability.total_material_cost.toFixed(2)} <br> (${availability.total_materials} items)
                        </span>
                    `;
                }

                // Show success state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-success mb-0 p-2 text-center">
                            <i class="fas fa-dollar-sign me-2"></i>
                            <strong>$${availability.total_material_cost.toFixed(2)} Material Cost</strong>
                            <br><small>${availability.total_materials} items across ${availability.tasks_with_materials} tasks</small>
                        </div>
                    `;
                }

                // Show success state for mobile view
                updateMobileButtonState(mobileButton, 'cost', {
                    amount: availability.total_material_cost.toFixed(2),
                    title: `$${availability.total_material_cost.toFixed(2)} material cost from ${availability.total_materials} items across ${availability.tasks_with_materials} tasks`
                });
            } else {
                // Show no material cost state for table view
                if (container) {
                    container.innerHTML = `
                        <span class="badge bg-secondary material-cost-badge" title="No material cost found">
                            <i class="fas fa-dollar-sign me-1"></i>
                            $0.00
                        </span>
                    `;
                }

                // Show no material cost state for card view
                if (cardContainer) {
                    cardContainer.innerHTML = `
                        <div class="alert alert-secondary mb-0 p-2 text-center">
                            <i class="fas fa-dollar-sign me-2"></i>
                            <strong>No Material Cost</strong>
                            <br><small>No material cost found</small>
                        </div>
                    `;
                }

                // Show no material cost state for mobile view
                updateMobileButtonState(mobileButton, 'cost', {
                    amount: '0.00',
                    title: 'No material cost found'
                });
            }

            console.log(`💰 Material cost check for WO ${wonum}: ${availability.total_materials} items, $${availability.total_material_cost.toFixed(2)} in ${checkTime.toFixed(3)}s${availability.cache_hit ? ' (cached)' : ''}`);

        } else {
            // Show error state for table view
            if (container) {
                container.innerHTML = `
                    <span class="badge bg-danger material-cost-badge" title="Error checking material cost: ${result.error}">
                        <i class="fas fa-exclamation-triangle me-1"></i>
                        Error
                    </span>
                `;
            }

            // Show error state for card view
            if (cardContainer) {
                cardContainer.innerHTML = `
                    <div class="alert alert-danger mb-0 p-2 text-center">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Error</strong>
                        <br><small>${result.error}</small>
                    </div>
                `;
            }

            // Show error state for mobile view
            updateMobileButtonState(mobileButton, 'error', {
                title: `Error checking material cost: ${result.error}`
            });

            console.error('Error checking material cost:', result.error);
        }

    } catch (error) {
        console.error('Error checking material cost:', error);

        // Show error state for table view
        if (container) {
            container.innerHTML = `
                <span class="badge bg-danger material-cost-badge" title="Network error: ${error.message}">
                    <i class="fas fa-exclamation-triangle me-1"></i>
                    Error
                </span>
            `;
        }

        // Show error state for card view
        if (cardContainer) {
            cardContainer.innerHTML = `
                <div class="alert alert-danger mb-0 p-2 text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <strong>Network Error</strong>
                    <br><small>${error.message}</small>
                </div>
            `;
        }

        // Show error state for mobile view
        updateMobileButtonState(mobileButton, 'error', {
            title: `Network error: ${error.message}`
        });
    } finally {
        // Clean up the checking flag
        if (window.checkingMaterialCost) {
            delete window.checkingMaterialCost[wonum];
        }
    }
}

// Individual work order actions
async function executeIndividualMethod(methodName, wonum) {
    if (!confirm(`Are you sure you want to execute "${methodName}" on work order ${wonum}?`)) {
        return;
    }

    try {
        // Map method names to status codes for signature checking
        const statusMap = {
            'approve': 'APPR',
            'start': 'INPRG',
            'complete': 'COMP'
        };

        const newStatus = statusMap[methodName];

        // If this method maps to a status, check for signature requirement first
        if (newStatus) {
            const signatureCheck = await fetch('/api/admin/signature-required', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    status: newStatus,
                    wo_type: 'parent'
                })
            });

            const signatureResult = await signatureCheck.json();

            if (signatureResult.success && signatureResult.signature_required) {
                // Show signature modal instead of proceeding directly
                showSignatureModal(wonum, newStatus, 'parent');
                return;
            }
        }

        const response = await fetch(`/api/workorder/${wonum}/${methodName}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({})
        });

        const result = await response.json();

        if (result.success) {
            alert(`Successfully executed ${methodName} on work order ${wonum}`);
            // Refresh current search
            performSearch(currentPage);
        } else if (result.signature_required) {
            // Handle signature requirement from the endpoint itself
            showSignatureModal(wonum, result.status, 'parent');
        } else {
            alert(`Error executing ${methodName}: ${result.error || 'Unknown error'}`);
        }

    } catch (error) {
        alert(`Network error: ${error.message}`);
    }
}

// Function to refresh materials check for a specific work order
async function refreshMaterialsCheck(wonum, siteid) {
    console.log(`🔄 Refreshing materials check for WO ${wonum}`);

    try {
        // Clear materials cache first
        const cacheResponse = await fetch('/api/task/planned-materials/cache/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const cacheResult = await cacheResponse.json();
        if (cacheResult.success) {
            console.log('✅ Materials cache cleared, re-checking materials...');

            // Re-run the materials check
            await checkMaterials(wonum, siteid);
        } else {
            console.error('❌ Failed to clear materials cache:', cacheResult.error);
        }
    } catch (error) {
        console.error('❌ Error refreshing materials check:', error);
    }
}

// Global function to refresh all materials checks (called after material addition)
async function refreshAllMaterialsChecks() {
    console.log('🔄 Refreshing all materials checks after material addition...');

    try {
        // Clear materials cache first
        const cacheResponse = await fetch('/api/task/planned-materials/cache/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const cacheResult = await cacheResponse.json();
        if (cacheResult.success) {
            console.log('✅ Materials cache cleared for all work orders');

            // Find all materials badges that are currently showing data (not buttons)
            const materialsBadges = document.querySelectorAll('.materials-badge');
            materialsBadges.forEach(badge => {
                const container = badge.closest('.materials-check-container');
                if (container) {
                    const containerId = container.id;
                    const wonum = containerId.replace('materials-', '');

                    // Reset to button state for re-checking
                    container.innerHTML = `
                        <button class="btn btn-sm btn-outline-secondary materials-check-btn"
                                onclick="checkMaterials('${wonum}', '')"
                                title="Check for planned materials">
                            <i class="fas fa-boxes"></i>
                            <span class="d-none d-md-inline ms-1">Check Materials</span>
                        </button>
                    `;
                }
            });

            console.log('🔄 Reset materials check buttons - user can re-check as needed');
        } else {
            console.error('❌ Failed to clear materials cache:', cacheResult.error);
        }
    } catch (error) {
        console.error('❌ Error refreshing all materials checks:', error);
    }
}

// Function to refresh labor hours check for a specific work order
async function refreshLaborHoursCheck(wonum, siteid) {
    console.log(`🔄 Refreshing labor hours check for WO ${wonum}`);

    try {
        // Clear labor cache first
        const cacheResponse = await fetch('/api/task/labor/cache/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const cacheResult = await cacheResponse.json();
        if (cacheResult.success) {
            console.log('✅ Labor cache cleared, re-checking labor hours...');

            // Re-run the labor hours check
            await checkLaborHours(wonum, siteid);
        } else {
            console.error('❌ Failed to clear labor cache:', cacheResult.error);
        }
    } catch (error) {
        console.error('❌ Error refreshing labor hours check:', error);
    }
}

// Global function to refresh all labor hours checks (called after labor addition)
async function refreshAllLaborHoursChecks() {
    console.log('🔄 Refreshing all labor hours checks after labor addition...');

    try {
        // Clear labor cache first
        const cacheResponse = await fetch('/api/task/labor/cache/clear', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const cacheResult = await cacheResponse.json();
        if (cacheResult.success) {
            console.log('✅ Labor cache cleared for all work orders');

            // Find all labor badges that are currently showing data (not buttons)
            const laborBadges = document.querySelectorAll('.labor-badge');
            laborBadges.forEach(badge => {
                const container = badge.closest('.labor-check-container');
                if (container) {
                    const containerId = container.id;
                    const wonum = containerId.replace('labor-', '');

                    // Reset to button state for re-checking
                    container.innerHTML = `
                        <button class="btn btn-sm btn-outline-secondary labor-check-btn"
                                onclick="checkLaborHours('${wonum}', '')"
                                title="Check for labor hours">
                            <i class="fas fa-user-clock"></i>
                            <span class="d-none d-md-inline ms-1">Check Labor</span>
                        </button>
                    `;
                }
            });

            console.log('🔄 Reset labor check buttons - user can re-check as needed');
        } else {
            console.error('❌ Failed to clear labor cache:', cacheResult.error);
        }
    } catch (error) {
        console.error('❌ Error refreshing all labor hours checks:', error);
    }
}

// Task details modal functionality
async function showTaskDetails(wonum) {
    try {
        // Show loading modal
        const modal = new bootstrap.Modal(document.getElementById('taskDetailsModal'));
        document.getElementById('taskDetailsModalLabel').textContent = `Task Details - WO ${wonum}`;
        document.getElementById('taskDetailsContent').innerHTML = `
            <div class="text-center p-4">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="mt-2">Loading task details...</p>
            </div>
        `;
        modal.show();

        // Fetch task details
        const response = await fetch(`/api/workorder/${wonum}/task-details`);
        const result = await response.json();

        if (result.success && result.details.success) {
            const tasks = result.details.tasks;

            if (tasks.length === 0) {
                document.getElementById('taskDetailsContent').innerHTML = `
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>
                        No tasks found for this work order.
                    </div>
                `;
                return;
            }

            // Build task details table
            let tableHtml = `
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead class="table-light">
                            <tr>
                                <th>Task</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th class="text-center">Regular Hours</th>
                                <th class="text-center">Labor Cost</th>
                                <th class="text-center">Materials</th>
                                <th class="text-center">Material Cost</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            let totalHours = 0;
            let totalLaborCost = 0;
            let totalMaterials = 0;
            let totalMaterialCost = 0;

            tasks.forEach(task => {
                totalHours += task.regular_hours;
                totalLaborCost += task.labor_cost;
                totalMaterials += task.material_count;
                totalMaterialCost += task.material_cost;

                tableHtml += `
                    <tr>
                        <td><strong class="text-primary">${task.wonum}</strong></td>
                        <td>
                            <div class="text-truncate" style="max-width: 200px;" title="${task.description || ''}">
                                ${task.description || '-'}
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-secondary">${task.status || '-'}</span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-clock me-1"></i>${task.regular_hours.toFixed(2)}h
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-warning text-dark">
                                <i class="fas fa-money-bill-wave me-1"></i>$${task.labor_cost.toFixed(2)}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-info">
                                <i class="fas fa-boxes me-1"></i>${task.material_count}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="badge bg-success">
                                <i class="fas fa-dollar-sign me-1"></i>$${task.material_cost.toFixed(2)}
                            </span>
                        </td>
                    </tr>
                `;
            });

            // Add totals row
            tableHtml += `
                        </tbody>
                        <tfoot class="table-dark">
                            <tr>
                                <th colspan="3">TOTALS (${tasks.length} tasks)</th>
                                <th class="text-center">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-clock me-1"></i>${totalHours.toFixed(2)}h
                                    </span>
                                </th>
                                <th class="text-center">
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-money-bill-wave me-1"></i>$${totalLaborCost.toFixed(2)}
                                    </span>
                                </th>
                                <th class="text-center">
                                    <span class="badge bg-info">
                                        <i class="fas fa-boxes me-1"></i>${totalMaterials}
                                    </span>
                                </th>
                                <th class="text-center">
                                    <span class="badge bg-success">
                                        <i class="fas fa-dollar-sign me-1"></i>$${totalMaterialCost.toFixed(2)}
                                    </span>
                                </th>
                            </tr>
                        </tfoot>
                    </table>
                </div>
            `;

            document.getElementById('taskDetailsContent').innerHTML = tableHtml;

        } else {
            document.getElementById('taskDetailsContent').innerHTML = `
                <div class="alert alert-danger text-center">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Error loading task details: ${result.error || 'Unknown error'}
                </div>
            `;
        }

    } catch (error) {
        console.error('Error showing task details:', error);
        document.getElementById('taskDetailsContent').innerHTML = `
            <div class="alert alert-danger text-center">
                <i class="fas fa-exclamation-triangle me-2"></i>
                Network error: ${error.message}
            </div>
        `;
    }
}

// Signature Capture Functionality
let signaturePad = null;
let signatureModal = null;
let pendingStatusChange = null;

// Initialize signature functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    const signatureModalElement = document.getElementById('signatureModal');

    if (signatureModalElement) {
        signatureModal = new bootstrap.Modal(signatureModalElement);

        // Initialize signature pad when modal is shown
        signatureModalElement.addEventListener('shown.bs.modal', function() {
            initializeSignaturePad();
            updateSignatureDateTime();
        });

        // Clean up when modal is hidden
        signatureModalElement.addEventListener('hidden.bs.modal', function() {
            clearSignature();
            pendingStatusChange = null;
        });

        console.log('✅ Signature modal initialized successfully');
    } else {
        console.error('❌ Signature modal element not found during initialization');
    }
});

function initializeSignaturePad() {
    const canvas = document.getElementById('signaturePad');
    const overlay = document.getElementById('signaturePadOverlay');

    if (!canvas) return;

    // Set canvas size
    const rect = canvas.getBoundingClientRect();
    canvas.width = rect.width;
    canvas.height = rect.height;

    const ctx = canvas.getContext('2d');
    ctx.strokeStyle = '#000000';
    ctx.lineWidth = 2;
    ctx.lineCap = 'round';
    ctx.lineJoin = 'round';

    let isDrawing = false;
    let hasSignature = false;

    // Mouse events
    canvas.addEventListener('mousedown', startDrawing);
    canvas.addEventListener('mousemove', draw);
    canvas.addEventListener('mouseup', stopDrawing);
    canvas.addEventListener('mouseout', stopDrawing);

    // Touch events for mobile
    canvas.addEventListener('touchstart', handleTouch);
    canvas.addEventListener('touchmove', handleTouch);
    canvas.addEventListener('touchend', stopDrawing);

    function startDrawing(e) {
        isDrawing = true;
        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        ctx.beginPath();
        ctx.moveTo(x, y);

        // Hide overlay on first draw
        if (!hasSignature) {
            if (overlay) {
                overlay.classList.add('hidden');
            }
            canvas.parentElement.classList.add('active');
            hasSignature = true;
            validateSignatureForm();
        }
    }

    function draw(e) {
        if (!isDrawing) return;

        const rect = canvas.getBoundingClientRect();
        const x = e.clientX - rect.left;
        const y = e.clientY - rect.top;

        ctx.lineTo(x, y);
        ctx.stroke();
    }

    function stopDrawing() {
        isDrawing = false;
        ctx.beginPath();
    }

    function handleTouch(e) {
        e.preventDefault();
        const touch = e.touches[0];
        const mouseEvent = new MouseEvent(e.type === 'touchstart' ? 'mousedown' :
                                        e.type === 'touchmove' ? 'mousemove' : 'mouseup', {
            clientX: touch.clientX,
            clientY: touch.clientY
        });
        canvas.dispatchEvent(mouseEvent);
    }

    // Store reference for clearing
    signaturePad = {
        canvas: canvas,
        context: ctx,
        overlay: overlay,
        hasSignature: () => hasSignature,
        clear: () => {
            ctx.clearRect(0, 0, canvas.width, canvas.height);
            if (overlay) {
                overlay.classList.remove('hidden');
            }
            canvas.parentElement.classList.remove('active');
            hasSignature = false;
            validateSignatureForm();
        }
    };
}

function clearSignature() {
    if (signaturePad) {
        signaturePad.clear();
    }
}

function updateSignatureDateTime() {
    const now = new Date();
    const dateTimeString = now.toLocaleString();
    const dateTimeElement = document.getElementById('signatureDateTime');
    if (dateTimeElement) {
        dateTimeElement.value = dateTimeString;
    } else {
        console.error('❌ signatureDateTime element not found');
    }
}

function validateSignatureForm() {
    const customerNameElement = document.getElementById('signatureCustomerName');
    const submitBtn = document.getElementById('submitSignatureBtn');

    if (!customerNameElement || !submitBtn) {
        console.error('❌ Required signature form elements not found');
        return;
    }

    const customerName = customerNameElement.value.trim();
    const hasSignature = signaturePad && signaturePad.hasSignature();

    submitBtn.disabled = !customerName || !hasSignature;
}

// Add event listeners for form validation
const signatureCustomerNameElement = document.getElementById('signatureCustomerName');
if (signatureCustomerNameElement) {
    signatureCustomerNameElement.addEventListener('input', validateSignatureForm);
} else {
    console.error('❌ signatureCustomerName element not found for event listener');
}

function showSignatureModal(wonum, newStatus, woType = 'parent') {
    // Store pending status change
    pendingStatusChange = { wonum, newStatus, woType };

    // Update modal information with null checks
    const statusInfoElement = document.getElementById('signatureStatusInfo');
    const wonumInfoElement = document.getElementById('signatureWonumInfo');
    const customerNameElement = document.getElementById('signatureCustomerName');
    const commentsElement = document.getElementById('signatureComments');

    if (statusInfoElement) {
        statusInfoElement.textContent = newStatus;
    } else {
        console.error('❌ signatureStatusInfo element not found');
    }

    if (wonumInfoElement) {
        wonumInfoElement.textContent = wonum;
    } else {
        console.error('❌ signatureWonumInfo element not found');
    }

    // Clear form with null checks
    if (customerNameElement) {
        customerNameElement.value = '';
    } else {
        console.error('❌ signatureCustomerName element not found');
    }

    if (commentsElement) {
        commentsElement.value = '';
    } else {
        console.error('❌ signatureComments element not found');
    }

    // Show modal with null check
    if (signatureModal) {
        signatureModal.show();
    } else {
        console.error('❌ signatureModal not initialized');
        alert('Signature modal not available. Please refresh the page.');
    }
}

function submitSignature() {
    if (!pendingStatusChange) {
        alert('No pending status change');
        return;
    }

    const customerNameElement = document.getElementById('signatureCustomerName');
    const commentsElement = document.getElementById('signatureComments');
    const dateTimeElement = document.getElementById('signatureDateTime');

    if (!customerNameElement || !commentsElement || !dateTimeElement) {
        alert('Signature form elements not found. Please refresh the page.');
        return;
    }

    const customerName = customerNameElement.value.trim();
    const comments = commentsElement.value.trim();
    const dateTime = dateTimeElement.value;

    if (!customerName) {
        alert('Customer name is required');
        return;
    }

    if (!signaturePad || !signaturePad.hasSignature()) {
        alert('Signature is required');
        return;
    }

    // Get signature as base64
    const signatureData = signaturePad.canvas.toDataURL('image/png');

    const submitBtn = document.getElementById('submitSignatureBtn');
    if (!submitBtn) {
        alert('Submit button not found. Please refresh the page.');
        return;
    }

    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Processing...';
    submitBtn.disabled = true;

    // Submit signature and process status change
    const signaturePayload = {
        wonum: pendingStatusChange.wonum,
        status: pendingStatusChange.newStatus,
        wo_type: pendingStatusChange.woType,
        signature_data: signatureData,
        customer_name: customerName,
        comments: comments,
        date_time: dateTime
    };

    console.log('📝 Submitting signature payload:', {
        wonum: signaturePayload.wonum,
        status: signaturePayload.status,
        wo_type: signaturePayload.wo_type,
        customer_name: signaturePayload.customer_name,
        has_signature: !!signaturePayload.signature_data
    });

    fetch('/api/signature/submit', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(signaturePayload)
    })
    .then(response => {
        console.log('📝 Signature submission response status:', response.status);
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log('📝 Signature submission result:', data);
        if (data.success) {
            alert('Signature captured and status updated successfully!');
            signatureModal.hide();

            // Refresh the work orders list
            performSearch(currentPage);
        } else {
            console.error('❌ Signature submission failed:', data);
            alert(`Failed to process signature: ${data.error}`);

            // Show additional details if available
            if (data.pdf_generated && !data.pdf_attached) {
                alert('Note: PDF generated but attachment to Maximo failed');
            }
        }
    })
    .catch(error => {
        console.error('❌ Error submitting signature:', error);

        // Provide more specific error messages
        if (error.message.includes('Failed to fetch')) {
            alert('Network connection error. Please check your connection and try again.');
        } else if (error.message.includes('HTTP 500')) {
            alert('Server error during signature processing. Please try again.');
        } else if (error.message.includes('HTTP 401')) {
            alert('Authentication error. Please refresh the page and login again.');
        } else {
            alert(`Network error: ${error.message}`);
        }
    })
    .finally(() => {
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    });
}

// Store work order list for detail page navigation
function storeWorkOrderListForNavigation(workorders) {
    try {
        // Store simplified work order data for navigation
        const navigationData = workorders.map(wo => ({
            wonum: wo.wonum,
            description: wo.description,
            status: wo.status,
            siteid: wo.siteid
        }));
        sessionStorage.setItem('workOrderList', JSON.stringify(navigationData));
    } catch (error) {
        console.error('Error storing work order list for navigation:', error);
    }
}

// Helper function to update mobile button states
function updateMobileButtonState(mobileButton, state, data = {}) {
    if (!mobileButton) return;

    mobileButton.disabled = false;

    switch (state) {
        case 'loading':
            mobileButton.disabled = true;
            mobileButton.innerHTML = '<div class="spinner-border spinner-border-sm me-2" role="status"></div>Loading...';
            break;

        case 'success':
            mobileButton.innerHTML = `<i class="fas fa-check-circle me-1"></i>${data.count || 0} Found`;
            mobileButton.className = 'btn btn-success mobile-check-btn';
            mobileButton.title = data.title || 'Success';
            break;

        case 'none':
            mobileButton.innerHTML = `<i class="fas fa-box-open me-1"></i>None Found`;
            mobileButton.className = 'btn btn-secondary mobile-check-btn';
            mobileButton.title = data.title || 'None found';
            break;

        case 'error':
            mobileButton.innerHTML = `<i class="fas fa-exclamation-triangle me-1"></i>Error`;
            mobileButton.className = 'btn btn-danger mobile-check-btn';
            mobileButton.title = data.title || 'Error occurred';
            break;

        case 'cost':
            mobileButton.innerHTML = `<i class="fas fa-dollar-sign me-1"></i>$${data.amount || '0.00'}`;
            mobileButton.className = 'btn btn-info mobile-check-btn';
            mobileButton.title = data.title || `Total cost: $${data.amount || '0.00'}`;
            break;
    }
}

// Add loading overlay for work order detail navigation
document.addEventListener('DOMContentLoaded', function() {
    // Add click handlers to work order detail links
    document.addEventListener('click', function(e) {
        if (e.target.closest('.workorder-detail-link')) {
            // Show loading overlay
            const overlay = document.createElement('div');
            overlay.id = 'navigationLoadingOverlay';
            overlay.className = 'workorder-loading-overlay';
            overlay.style.cssText = 'position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(248, 249, 250, 0.95); z-index: 1018; display: flex; flex-direction: column; align-items: center; justify-content: center; backdrop-filter: blur(5px);';
            overlay.innerHTML = `
                <div style="width: 48px; height: 48px; border: 4px solid #e9ecef; border-top: 4px solid #007bff; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 1rem;"></div>
                <div class="h5 text-primary mb-2">Loading Work Order Details</div>
                <div class="text-muted">Please wait while we fetch the information...</div>
            `;
            document.body.appendChild(overlay);
        }
    });
});
</script>

<!-- Task Details Modal -->
<div class="modal fade" id="taskDetailsModal" tabindex="-1" aria-labelledby="taskDetailsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="taskDetailsModalLabel">Task Details</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="taskDetailsContent">
                    <!-- Content will be loaded here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Signature Capture Modal -->
<div class="modal fade" id="signatureModal" tabindex="-1" aria-labelledby="signatureModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="signatureModalLabel">
                    <i class="fas fa-signature me-2"></i>Digital Signature Required
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="signature-info mb-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <strong>Signature Required:</strong> This status change requires a digital signature for audit compliance.
                        <br><small>Status: <span id="signatureStatusInfo">-</span> | Work Order: <span id="signatureWonumInfo">-</span></small>
                    </div>
                </div>

                <!-- Customer Information -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="signatureCustomerName" class="form-label">Customer Name *</label>
                        <input type="text" class="form-control" id="signatureCustomerName"
                               placeholder="Enter customer name" required>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">Date & Time</label>
                        <input type="text" class="form-control" id="signatureDateTime" readonly>
                    </div>
                </div>

                <!-- Comments -->
                <div class="mb-3">
                    <label for="signatureComments" class="form-label">Additional Comments</label>
                    <textarea class="form-control" id="signatureComments" rows="3"
                              placeholder="Enter any additional comments (optional)"></textarea>
                </div>

                <!-- Signature Pad -->
                <div class="signature-pad-container">
                    <label class="form-label">Digital Signature *</label>
                    <div class="signature-pad-wrapper">
                        <canvas id="signaturePad" width="600" height="200"></canvas>
                        <div class="signature-pad-overlay" id="signaturePadOverlay">
                            <i class="fas fa-pen-fancy fa-2x text-muted"></i>
                            <p class="text-muted mt-2">Click and drag to sign</p>
                        </div>
                    </div>
                    <div class="signature-pad-controls mt-2">
                        <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearSignature()">
                            <i class="fas fa-eraser me-1"></i>Clear
                        </button>
                        <small class="text-muted ms-3">Use mouse or touch to create your signature</small>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>Cancel
                </button>
                <button type="button" class="btn btn-primary" id="submitSignatureBtn" onclick="submitSignature()" disabled>
                    <i class="fas fa-check me-1"></i>Submit Signature & Continue
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}
