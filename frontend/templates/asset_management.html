{% extends "base.html" %}

{% block title %}Asset Management{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/asset_management.css') }}">
{% endblock %}

{% block extra_css %}
<!-- Select2 CSS for searchable dropdowns -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="page-header">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="page-title">
                            <i class="fas fa-cogs me-2"></i>Asset Management
                        </h1>
                        <p class="page-subtitle text-muted">
                            Search and manage assets across all locations
                        </p>
                    </div>
                    <div class="header-actions">

                        <button class="btn btn-outline-secondary" onclick="clearAssetCache()" title="Clear Cache">
                            <i class="fas fa-sync-alt me-1"></i>
                            <span class="d-none d-md-inline">Refresh</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card search-card">
                <div class="card-body">
                    <form id="assetManagementSearchForm">
                        <!-- Compact Search Layout -->
                        <div class="compact-search-layout">
                            <!-- Search Input Row -->
                            <div class="search-input-row">
                                <div class="search-input-group">
                                    <label for="assetSearchTerm" class="compact-label">
                                        <i class="fas fa-search"></i>
                                        <span>Search Assets</span>
                                    </label>
                                    <input type="text"
                                           class="form-control search-input-expanded"
                                           id="assetSearchTerm"
                                           placeholder="Enter asset number, description, asset tag, serial number, or model..."
                                           autocomplete="off">
                                    <div class="search-help-text">
                                        Search by asset number, description, asset tag, serial number, or model. Supports partial matching.
                                    </div>
                                </div>
                                <button type="submit" class="btn btn-primary search-btn">
                                    <i class="fas fa-search"></i>
                                    <span class="d-none d-sm-inline ms-1">Search</span>
                                </button>
                            </div>

                            <!-- Filters Grid -->
                            <div class="filters-grid">
                                <div class="filter-group">
                                    <label for="assetSiteFilter" class="compact-label">
                                        <i class="fas fa-building"></i>
                                        <span>Site</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="assetSiteFilter" name="site_id">
                                        <option value="">All Sites</option>
                                        <option value="">Loading...</option>
                                    </select>
                                </div>

                                <div class="filter-group">
                                    <label for="assetStatusFilter" class="compact-label">
                                        <i class="fas fa-flag"></i>
                                        <span>Status</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="assetStatusFilter">
                                        <option value="">All Active</option>
                                        <option value="OPERATING">Operating</option>
                                        <option value="ACTIVE">Active</option>
                                        <option value="INACTIVE">Inactive</option>
                                        <option value="BROKEN">Broken</option>
                                        <option value="MISSING">Missing</option>
                                    </select>
                                </div>

                                <div class="filter-group">
                                    <label for="assetTypeFilter" class="compact-label">
                                        <i class="fas fa-layer-group"></i>
                                        <span>Type</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="assetTypeFilter">
                                        <option value="">All Types</option>
                                        <option value="PRODUCTION">Production</option>
                                        <option value="FACILITIES">Facilities</option>
                                        <option value="IT">IT Equipment</option>
                                        <option value="VEHICLE">Vehicle</option>
                                        <option value="TOOL">Tool</option>
                                    </select>
                                </div>

                                <div class="filter-group">
                                    <label for="assetLimitFilter" class="compact-label">
                                        <i class="fas fa-list-ol"></i>
                                        <span>Results</span>
                                    </label>
                                    <select class="form-select form-select-sm" id="assetLimitFilter">
                                        <option value="10">10</option>
                                        <option value="20" selected>20</option>
                                        <option value="50">50</option>
                                        <option value="100">100</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Results Section -->
    <div class="row">
        <div class="col-12">
            <div class="card results-card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>Search Results
                        </h5>
                        <small class="text-muted" id="assetResultsInfo">Enter search criteria to find assets</small>
                    </div>
                    <div class="results-actions">
                        <button class="btn btn-sm btn-outline-secondary" onclick="exportAssetResults()" title="Export Results" style="display: none;" id="exportAssetsBtn">
                            <i class="fas fa-download me-1"></i>Export
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <!-- Loading Indicator -->
                    <div class="loading-container text-center py-5" id="assetLoadingIndicator" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">Searching assets...</p>
                    </div>

                    <!-- Loading Message -->
                    <div class="alert alert-info text-center" id="assetLoadingMessage" style="display: none;">
                        <i class="fas fa-spinner fa-spin me-2"></i>
                        <span>Loading...</span>
                    </div>

                    <!-- Error Message -->
                    <div class="alert alert-danger text-center" id="assetErrorMessage" style="display: none;">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <span>Error message will appear here</span>
                    </div>

                    <!-- Desktop Table View -->
                    <div class="table-responsive d-none d-md-block" id="assetTableView">
                        <table class="table table-hover mb-0" id="assetTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="50">
                                        <input type="checkbox" id="selectAllAssetsCheckbox" class="form-check-input">
                                    </th>
                                    <th><i class="fas fa-hashtag me-1"></i>Asset Number</th>
                                    <th><i class="fas fa-building me-1"></i>Site</th>
                                    <th><i class="fas fa-file-text me-1"></i>Description</th>
                                    <th><i class="fas fa-circle me-1"></i>Status</th>
                                    <th><i class="fas fa-map-marker-alt me-1"></i>Location</th>
                                    <th><i class="fas fa-tag me-1"></i>Asset Tag</th>
                                    <th><i class="fas fa-barcode me-1"></i>Serial #</th>
                                    <th><i class="fas fa-cube me-1"></i>Model</th>
                                    <th><i class="fas fa-layer-group me-1"></i>Type</th>
                                    <th><i class="fas fa-tools me-1"></i>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="assetTableBody">
                                <!-- Dynamic content will be inserted here -->
                            </tbody>
                        </table>
                    </div>

                    <!-- Mobile Card View -->
                    <div class="mobile-card-view d-md-none p-3" id="assetCardView">
                        <!-- Dynamic card content will be inserted here -->
                    </div>

                    <!-- No Results Message -->
                    <div class="no-results text-center py-5" id="assetNoResults" style="display: none;">
                        <i class="fas fa-search text-muted mb-3" style="font-size: 3rem;"></i>
                        <h5 class="text-muted">No assets found</h5>
                        <p class="text-muted">Try adjusting your search criteria or filters</p>
                    </div>
                </div>

                <!-- Pagination -->
                <div class="card-footer" id="assetPaginationContainer" style="display: none;">
                    <nav aria-label="Asset pagination">
                        <ul class="pagination pagination-sm justify-content-center mb-0" id="assetPagination">
                            <!-- Dynamic pagination will be inserted here -->
                        </ul>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Asset Detail Modal - Streamlined Mobile-First Design -->
<div class="modal fade" id="assetDetailModal" tabindex="-1" aria-labelledby="assetDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-fullscreen-sm-down modal-lg">
        <div class="modal-content asset-detail-modal">
            <div class="modal-header asset-detail-header">
                <h5 class="modal-title" id="assetDetailModalLabel">
                    <i class="fas fa-cogs me-2"></i>Asset Details
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body asset-detail-body" id="assetDetailModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Asset Actions Modal -->
<div class="modal fade" id="assetActionsModal" tabindex="-1" aria-labelledby="assetActionsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="assetActionsModalLabel">
                    <i class="fas fa-tools me-2"></i>Asset Actions
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="assetActionsModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Create Problem Modal -->
<div class="modal fade" id="createProblemModal" tabindex="-1" aria-labelledby="createProblemModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createProblemModalLabel">
                    <i class="fas fa-bug me-2"></i>Create Problem
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createProblemModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Work Order Modal -->
<div class="modal fade" id="createWorkOrderModal" tabindex="-1" aria-labelledby="createWorkOrderModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createWorkOrderModalLabel">
                    <i class="fas fa-wrench me-2"></i>Create Work Order
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createWorkOrderModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Service Request Modal -->
<div class="modal fade" id="createServiceRequestModal" tabindex="-1" aria-labelledby="createServiceRequestModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createServiceRequestModalLabel">
                    <i class="fas fa-headset me-2"></i>Create Service Request
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createServiceRequestModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Incident Modal -->
<div class="modal fade" id="createIncidentModal" tabindex="-1" aria-labelledby="createIncidentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createIncidentModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>Create Incident
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createIncidentModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Change Modal -->
<div class="modal fade" id="createChangeModal" tabindex="-1" aria-labelledby="createChangeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createChangeModalLabel">
                    <i class="fas fa-exchange-alt me-2"></i>Create Change
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createChangeModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Release Modal -->
<div class="modal fade" id="createReleaseModal" tabindex="-1" aria-labelledby="createReleaseModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createReleaseModalLabel">
                    <i class="fas fa-rocket me-2"></i>Create Release
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createReleaseModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Create Unscheduled Inspection Modal -->
<div class="modal fade" id="createInspectionModal" tabindex="-1" aria-labelledby="createInspectionModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="createInspectionModalLabel">
                    <i class="fas fa-clipboard-check me-2"></i>Create Unscheduled Inspection
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="createInspectionModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>

<!-- Report Downtime Modal -->
<div class="modal fade" id="reportDowntimeModal" tabindex="-1" aria-labelledby="reportDowntimeModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reportDowntimeModalLabel">
                    <i class="fas fa-clock me-2"></i>Report Downtime
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="reportDowntimeModalBody">
                <!-- Dynamic content will be inserted here -->
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Select2 JS for searchable dropdowns -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script src="{{ url_for('static', filename='js/asset_management.js') }}?v=modern-enhanced-2025"></script>
{% endblock %}
