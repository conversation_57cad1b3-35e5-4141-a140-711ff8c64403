<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test AI Proceed Frontend</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>Test AI Proceed with Creation</h2>
        <button id="testProceedBtn" class="btn btn-primary">Test Proceed with Creation</button>
        <div id="results" class="mt-3"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Mock the asset management class methods for testing
        class TestAssetManagement {
            async updateAIAnalysisDecision(analysisId, decision, createdWorkorderNum = null) {
                console.log('🔧 TEST: Updating AI analysis decision...');
                try {
                    const response = await fetch('/api/ai-analysis/update-decision', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            analysis_id: analysisId,
                            user_decision: decision,
                            created_workorder_num: createdWorkorderNum
                        })
                    });
                    
                    if (!response.ok) {
                        console.warn('Failed to update AI analysis decision');
                    }
                } catch (error) {
                    console.warn('Error updating AI analysis decision:', error);
                }
            }

            showAlert(message, type = 'info') {
                const alertClass = type === 'error' ? 'alert-danger' : `alert-${type}`;
                const iconClass = type === 'error' ? 'fas fa-exclamation-triangle' : 'fas fa-info-circle';
                
                const alertHtml = `
                    <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                        <i class="${iconClass} me-2"></i>${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;
                
                document.getElementById('results').innerHTML = alertHtml;
            }

            showWorkOrderCreationSuccess(result) {
                console.log('🎉 TEST: Showing work order creation success');
                const wonum = result.wonum || result.data?.wonum || 'Unknown';
                const assetnum = result.assetnum || result.data?.assetnum || 'Unknown';
                const siteid = result.siteid || result.data?.siteid || 'Unknown';
                
                const successHtml = `
                    <div class="alert alert-success" role="alert">
                        <h4 class="alert-heading">🎉 Work Order Created Successfully!</h4>
                        <p><strong>Work Order Number:</strong> ${wonum}</p>
                        <p><strong>Asset Number:</strong> ${assetnum}</p>
                        <p><strong>Site ID:</strong> ${siteid}</p>
                        <hr>
                        <p class="mb-0">The work order has been created in Maximo successfully.</p>
                    </div>
                `;
                
                document.getElementById('results').innerHTML = successHtml;
            }

            async createWorkOrderWithoutAI(formData) {
                try {
                    console.log('🔧 TEST: Starting work order creation...');
                    console.log('🔧 TEST: Form data:', formData);
                    
                    this.showAlert('Creating work order...', 'info');

                    const response = await fetch('/api/asset/create-workorder', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify(formData)
                    });

                    console.log('🔧 TEST: API response status:', response.status);
                    const result = await response.json();
                    console.log('🔧 TEST: API response data:', result);

                    if (result.success) {
                        console.log('🎉 TEST: Work order created successfully!');
                        console.log('🎉 TEST: Work order number:', result.wonum);
                        
                        // Update AI analysis with created work order number
                        if (window.currentAIAnalysis?.analysis_id && result.wonum) {
                            console.log('🔧 TEST: Updating AI analysis with work order number...');
                            await this.updateAIAnalysisDecision(
                                window.currentAIAnalysis.analysis_id,
                                'proceed',
                                result.wonum
                            );
                            console.log('🔧 TEST: AI analysis updated');
                        }

                        console.log('🔧 TEST: Showing success response...');
                        this.showWorkOrderCreationSuccess(result);
                        
                        console.log('✅ TEST: Process completed successfully');
                    } else {
                        console.error('❌ TEST: Work order creation failed:', result.error);
                        this.showAlert(result.error || 'Failed to create work order', 'error');
                    }
                } catch (error) {
                    console.error('❌ TEST: Network error:', error);
                    this.showAlert('Network error occurred while creating work order: ' + error.message, 'error');
                }
            }

            async proceedWithWorkOrderCreation(analysisResult, asset, formData) {
                try {
                    console.log('🔧 TEST: Starting work order creation process...');
                    console.log('🔧 TEST: Form data:', formData);

                    // Add acknowledgment note if there are high-risk duplicates
                    const duplicationAnalysis = analysisResult.duplication_analysis || {};
                    if (duplicationAnalysis.risk_level === 'HIGH') {
                        const highRiskCount = duplicationAnalysis.high_risk?.length || 0;
                        const acknowledgmentNote = `AI Analysis: User acknowledged ${highRiskCount} potential duplicate(s) and chose to proceed. Similarity score: ${Math.round((analysisResult.kpis?.highest_similarity_score || 0) * 100)}%`;

                        // Add to long description
                        if (formData.longdescription) {
                            formData.longdescription += `\n\n${acknowledgmentNote}`;
                        } else {
                            formData.longdescription = acknowledgmentNote;
                        }
                        console.log('🔧 TEST: Added AI acknowledgment note');
                    }

                    // Update user decision in database
                    if (analysisResult.analysis_id) {
                        console.log('🔧 TEST: Updating AI analysis decision...');
                        await this.updateAIAnalysisDecision(analysisResult.analysis_id, 'proceed');
                        console.log('🔧 TEST: AI analysis decision updated');
                    }

                    // Create the work order
                    console.log('🔧 TEST: Creating work order...');
                    await this.createWorkOrderWithoutAI(formData);
                    console.log('🔧 TEST: Work order creation completed');

                } catch (error) {
                    console.error('❌ TEST: Error in work order creation process:', error);
                    this.showAlert('Failed to proceed with work order creation: ' + error.message, 'error');
                }
            }
        }

        // Test data
        const testAssetManagement = new TestAssetManagement();
        
        const mockAnalysisResult = {
            analysis_id: 'test_analysis_123',
            kpis: { risk_level: 'NONE' },
            duplication_analysis: { risk_level: 'NONE' }
        };
        
        const mockAsset = {
            assetnum: '68980',
            siteid: 'LCVKWT'
        };
        
        const mockFormData = {
            assetnum: '68980',
            siteid: 'LCVKWT',
            orgid: 'USARMY',
            description: 'Test work order from frontend test',
            worktype: 'CM',
            priority: '3',
            longdescription: 'Testing the frontend proceed with creation flow',
            targstartdate: '2025-07-28T10:00',
            createdby: 'SOFG118757'
        };

        // Set up test button
        document.getElementById('testProceedBtn').addEventListener('click', async () => {
            console.log('🧪 Starting frontend test...');
            await testAssetManagement.proceedWithWorkOrderCreation(mockAnalysisResult, mockAsset, mockFormData);
        });
    </script>
</body>
</html>
