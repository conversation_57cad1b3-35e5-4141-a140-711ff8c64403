#!/usr/bin/env python3
"""
Test AI Work Order Analysis Feature

This script tests the AI-powered work order analysis and duplication detection feature.
"""

import requests
import json
import time

def test_ai_analysis():
    """Test the AI analysis feature with different scenarios."""
    
    base_url = "http://127.0.0.1:5010"
    
    print("🤖 TESTING AI WORK ORDER ANALYSIS FEATURE")
    print("=" * 60)
    
    # Test Case 1: Asset with potential existing work orders
    print("\n1️⃣ Testing AI Analysis for Asset with Potential Work Orders:")
    
    test_data = {
        "asset_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "Test Asset for AI Analysis"
        },
        "user_description": "Repair broken pump motor that is making loud noise and vibrating excessively",
        "form_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "orgid": "USARMY",
            "description": "Repair broken pump motor",
            "worktype": "CM",
            "priority": "2",
            "longdescription": "The pump motor is making loud noise and vibrating excessively. Need immediate repair.",
            "targstartdate": "2025-07-28T10:00",
            "createdby": "TEST_USER"
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/ai-analysis/analyze-workorder",
            json=test_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ AI Analysis Response:")
            print(json.dumps(result, indent=2))
            
            # Analyze the results
            if result.get('success'):
                kpis = result.get('kpis', {})
                insights = result.get('insights', {})
                duplication_analysis = result.get('duplication_analysis', {})
                
                print(f"\n📊 Analysis Summary:")
                print(f"   • Total Open Work Orders: {kpis.get('total_open_workorders', 0)}")
                print(f"   • Potential Duplicates: {kpis.get('potential_duplicates', 0)}")
                print(f"   • Highest Similarity: {round((kpis.get('highest_similarity_score', 0) * 100), 1)}%")
                print(f"   • Risk Level: {kpis.get('risk_level', 'UNKNOWN')}")
                print(f"   • Analysis Duration: {result.get('analysis_duration_ms', 0)}ms")
                
                # Test duplication detection
                high_risk = duplication_analysis.get('high_risk', [])
                medium_risk = duplication_analysis.get('medium_risk', [])
                
                if high_risk:
                    print(f"\n⚠️ HIGH RISK DUPLICATES DETECTED ({len(high_risk)}):")
                    for i, dup in enumerate(high_risk[:3]):  # Show first 3
                        wo = dup['workorder']
                        similarity = round(dup['similarity_score'] * 100, 1)
                        print(f"   {i+1}. WO {wo['wonum']} - {similarity}% similar")
                        print(f"      Description: {wo['description']}")
                
                if medium_risk:
                    print(f"\n⚠️ MEDIUM RISK DUPLICATES DETECTED ({len(medium_risk)}):")
                    for i, dup in enumerate(medium_risk[:2]):  # Show first 2
                        wo = dup['workorder']
                        similarity = round(dup['similarity_score'] * 100, 1)
                        print(f"   {i+1}. WO {wo['wonum']} - {similarity}% similar")
                        print(f"      Description: {wo['description']}")
                
                # Test recommendations
                recommendations = result.get('recommendations', [])
                if recommendations:
                    print(f"\n💡 AI Recommendations ({len(recommendations)}):")
                    for i, rec in enumerate(recommendations):
                        print(f"   {i+1}. [{rec['type']}] {rec['title']}")
                        print(f"      {rec['message']}")
                
            else:
                print(f"❌ AI Analysis Failed: {result.get('error')}")
        else:
            print(f"❌ Request Failed: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Test Case 2: Test with different description to check similarity detection
    print("\n\n2️⃣ Testing Similarity Detection with Different Description:")
    
    test_data2 = {
        "asset_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "Test Asset for AI Analysis"
        },
        "user_description": "Fix motor noise and vibration issues on pump equipment",
        "form_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "Fix motor noise and vibration issues",
            "worktype": "CM",
            "priority": "3"
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/ai-analysis/analyze-workorder",
            json=test_data2,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                kpis = result.get('kpis', {})
                print(f"✅ Second Analysis:")
                print(f"   • Risk Level: {kpis.get('risk_level', 'UNKNOWN')}")
                print(f"   • Similarity Score: {round((kpis.get('highest_similarity_score', 0) * 100), 1)}%")
                print(f"   • Duration: {result.get('analysis_duration_ms', 0)}ms")
            else:
                print(f"❌ Second Analysis Failed: {result.get('error')}")
        else:
            print(f"❌ Second Request Failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in second test: {str(e)}")
    
    # Test Case 3: Test decision update
    print("\n\n3️⃣ Testing Decision Update:")
    
    decision_data = {
        "analysis_id": f"ai_{int(time.time())}_6468980",
        "user_decision": "proceed",
        "created_workorder_num": "TEST123456"
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/ai-analysis/update-decision",
            json=decision_data,
            timeout=10
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print("✅ Decision Update Successful")
            else:
                print(f"❌ Decision Update Failed: {result.get('error')}")
        else:
            print(f"❌ Decision Update Request Failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error in decision update: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🎯 AI ANALYSIS TESTING COMPLETE")

if __name__ == "__main__":
    test_ai_analysis()
