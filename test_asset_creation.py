#!/usr/bin/env python3
"""
Test script for asset creation functionality
"""
import requests
import json

def test_asset_creation_endpoints():
    """Test the asset creation API endpoints"""
    
    base_url = "http://127.0.0.1:5010"
    
    # Test data for work order creation (using valid asset number from search results)
    wo_data = {
        "assetnum": "68980",
        "siteid": "LCVKWT",
        "orgid": "USARMY",
        "description": "Test work order from asset management - Enhanced",
        "worktype": "CM",
        "priority": "3",
        "longdescription": "This is a test work order created from the enhanced asset management interface with work order number extraction",
        "createdby": "SOFG118757",  # Use actual personid
        "targstartdate": "2025-07-28T10:00"
    }

    # Test data for service request creation (using valid asset number from search results)
    sr_data = {
        "assetnum": "68980",
        "siteid": "LCVKWT",
        "orgid": "USARMY",
        "description": "Test service request from asset management",
        "priority": "3",
        "longdescription": "This is a test service request created from the asset management interface",
        "requestedby": "TEST_USER",
        "ownergroup": "MAINT",  # Add owner group for service requests
        "requesteddate": "2025-07-28T14:00"
    }
    
    print("🔧 TESTING ASSET CREATION ENDPOINTS")
    print("=" * 50)
    
    # Test work order creation
    print("\n1️⃣ Testing Work Order Creation:")
    try:
        response = requests.post(
            f"{base_url}/api/asset/create-workorder",
            json=wo_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
    except Exception as e:
        print(f"Error: {str(e)}")
    
    # Test service request creation
    print("\n2️⃣ Testing Service Request Creation:")
    try:
        response = requests.post(
            f"{base_url}/api/asset/create-servicerequest",
            json=sr_data,
            timeout=30
        )
        
        print(f"Status Code: {response.status_code}")
        print(f"Response: {json.dumps(response.json(), indent=2)}")
        
    except Exception as e:
        print(f"Error: {str(e)}")

if __name__ == "__main__":
    test_asset_creation_endpoints()
