#!/usr/bin/env python3
"""
Test AI Analysis Proceed with Creation Fix

This script tests that the "Proceed with Creation" button actually creates work orders.
"""

import requests
import json
import time

def test_ai_proceed_creation():
    """Test the complete AI analysis and proceed with creation flow."""
    
    base_url = "http://127.0.0.1:5010"
    
    print("🔧 TESTING AI PROCEED WITH CREATION FIX")
    print("=" * 50)
    
    # Step 1: Perform AI Analysis
    print("\n1️⃣ Step 1: Performing AI Analysis...")
    
    test_data = {
        "asset_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "Test Asset for AI Analysis"
        },
        "user_description": "Test work order creation after AI analysis - proceed fix test",
        "form_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "orgid": "USARMY",
            "description": "Test work order creation after AI analysis",
            "worktype": "CM",
            "priority": "3",
            "longdescription": "Testing that proceed with creation actually creates the work order",
            "targstartdate": "2025-07-28T10:00",
            "createdby": "SOFG118757"
        }
    }
    
    try:
        # Perform AI analysis
        response = requests.post(
            f"{base_url}/api/ai-analysis/analyze-workorder",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            analysis_result = response.json()
            if analysis_result.get('success'):
                print("✅ AI Analysis completed successfully")
                print(f"   • Risk Level: {analysis_result.get('kpis', {}).get('risk_level', 'UNKNOWN')}")
                print(f"   • Open Work Orders: {analysis_result.get('kpis', {}).get('total_open_workorders', 0)}")
                print(f"   • Analysis ID: {analysis_result.get('analysis_id', 'N/A')}")
                
                # Step 2: Simulate "Proceed with Creation"
                print("\n2️⃣ Step 2: Simulating 'Proceed with Creation'...")
                
                # Update user decision first
                decision_data = {
                    "analysis_id": analysis_result.get('analysis_id'),
                    "user_decision": "proceed"
                }
                
                decision_response = requests.post(
                    f"{base_url}/api/ai-analysis/update-decision",
                    json=decision_data,
                    timeout=10
                )
                
                if decision_response.status_code == 200:
                    print("✅ User decision updated successfully")
                else:
                    print(f"⚠️ Decision update failed: {decision_response.status_code}")
                
                # Step 3: Create the work order (simulating the fixed createWorkOrderWithoutAI)
                print("\n3️⃣ Step 3: Creating Work Order...")
                
                # Add AI acknowledgment note if high risk
                form_data = test_data["form_data"].copy()
                duplication_analysis = analysis_result.get('duplication_analysis', {})
                if duplication_analysis.get('risk_level') == 'HIGH':
                    high_risk_count = len(duplication_analysis.get('high_risk', []))
                    similarity_score = round((analysis_result.get('kpis', {}).get('highest_similarity_score', 0) * 100))
                    acknowledgment_note = f"AI Analysis: User acknowledged {high_risk_count} potential duplicate(s) and chose to proceed. Similarity score: {similarity_score}%"
                    
                    if form_data.get('longdescription'):
                        form_data['longdescription'] += f"\n\n{acknowledgment_note}"
                    else:
                        form_data['longdescription'] = acknowledgment_note
                    
                    print(f"   • Added AI acknowledgment note for {high_risk_count} high-risk duplicates")
                
                # Create the work order
                wo_response = requests.post(
                    f"{base_url}/api/asset/create-workorder",
                    json=form_data,
                    timeout=30
                )
                
                if wo_response.status_code == 200:
                    wo_result = wo_response.json()
                    if wo_result.get('success'):
                        print("🎉 WORK ORDER CREATED SUCCESSFULLY!")
                        print(f"   • Work Order Number: {wo_result.get('wonum', 'N/A')}")
                        print(f"   • Asset Number: {wo_result.get('assetnum', 'N/A')}")
                        print(f"   • Site ID: {wo_result.get('siteid', 'N/A')}")
                        
                        # Step 4: Update AI analysis with created work order number
                        if analysis_result.get('analysis_id') and wo_result.get('wonum'):
                            print("\n4️⃣ Step 4: Updating AI Analysis with Work Order Number...")
                            
                            final_decision_data = {
                                "analysis_id": analysis_result.get('analysis_id'),
                                "user_decision": "proceed",
                                "created_workorder_num": wo_result.get('wonum')
                            }
                            
                            final_response = requests.post(
                                f"{base_url}/api/ai-analysis/update-decision",
                                json=final_decision_data,
                                timeout=10
                            )
                            
                            if final_response.status_code == 200:
                                print("✅ AI Analysis updated with work order number")
                            else:
                                print(f"⚠️ Failed to update AI analysis: {final_response.status_code}")
                        
                        print("\n🎯 COMPLETE FLOW TEST SUCCESSFUL!")
                        print("   The 'Proceed with Creation' fix is working correctly.")
                        
                    else:
                        print(f"❌ Work Order Creation Failed: {wo_result.get('error', 'Unknown error')}")
                else:
                    print(f"❌ Work Order API Request Failed: {wo_response.status_code}")
                    print(wo_response.text)
                    
            else:
                print(f"❌ AI Analysis Failed: {analysis_result.get('error', 'Unknown error')}")
        else:
            print(f"❌ AI Analysis Request Failed: {response.status_code}")
            print(response.text)
            
    except Exception as e:
        print(f"❌ Test Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🏁 AI PROCEED CREATION TEST COMPLETE")

if __name__ == "__main__":
    test_ai_proceed_creation()
