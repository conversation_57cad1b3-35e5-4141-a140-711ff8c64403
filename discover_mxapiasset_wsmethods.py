#!/usr/bin/env python3
"""
MXAPIASSET Web Service Methods Discovery Script

This script systematically discovers and documents all available web service methods (wsmethods)
offered by the MXAPIASSET endpoint in Maximo, specifically focusing on Create operations.

Author: Augment Agent
Date: 2025-01-27
"""

import requests
import json
import time
from typing import Dict, List, <PERSON>ple
import sys
import os

# Add the backend directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

try:
    from services.token_manager import TokenManager
except ImportError:
    # Fallback if token manager is not available
    class TokenManager:
        def __init__(self):
            self.base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"

class MXAPIAssetWSMethodDiscovery:
    def __init__(self):
        self.token_manager = TokenManager()
        self.api_key = "dj9sia0tu2s0sktv3oq815amtv06ior0ahlsn70o"  # From previous tests
        
    def discover_asset_wsmethods(self, endpoint_url: str) -> <PERSON><PERSON>[List[str], List[str], List[str]]:
        """
        Discover available WSMethods for MXAPIASSET endpoint.
        
        Returns:
            Tuple of (valid_methods, invalid_methods, error_methods)
        """
        print(f"\n🔍 Discovering WSMethods for: {endpoint_url}")
        print("=" * 80)
        
        # Comprehensive list of potential asset-related wsmethods
        potential_wsmethods = [
            # Asset creation operations
            "create",
            "createworkorder",
            "createservicerequest", 
            "createincident",
            "createticket",
            "createwo",
            "createsr",
            "createpm",
            "createjob",
            
            # Standard CRUD operations
            "update",
            "delete",
            "sync",
            "merge",
            "validate",
            
            # Asset management operations
            "move",
            "transfer",
            "changeowner",
            "changestatus",
            "changecondition",
            "retire",
            "decommission",
            "activate",
            "deactivate",
            
            # Asset hierarchy operations
            "addchild",
            "removechild",
            "moveasset",
            "changeparent",
            
            # Asset maintenance operations
            "schedule",
            "unschedule",
            "calibrate",
            "inspect",
            "repair",
            "replace",
            
            # Asset financial operations
            "depreciate",
            "revalue",
            "capitalize",
            "adjustcost",
            
            # Asset workflow operations
            "approve",
            "reject",
            "submit",
            "complete",
            "cancel",
            "hold",
            "resume",
            
            # Asset reporting operations
            "gethistory",
            "getworkorders",
            "getservicerequests",
            "getincidents",
            "getmaintenance",
            "getcosts",
            "getdowntime",
            
            # Asset relationship operations
            "addrelationship",
            "removerelationship",
            "linkasset",
            "unlinkasset",
            
            # Asset condition operations
            "addcondition",
            "updatecondition",
            "removecondition",
            "assesscondition",
            
            # Asset meter operations
            "addmeter",
            "updatemeter",
            "removemeter",
            "readmeter",
            
            # Asset spare parts operations
            "addsparepart",
            "removesparepart",
            "reservepart",
            "issuepart",
            
            # Asset warranty operations
            "addwarranty",
            "updatewarranty",
            "removewarranty",
            "checkwarranty",
            
            # Asset safety operations
            "lockout",
            "tagout",
            "unlock",
            "untag",
            "safetycheckout",
            "safetycheckin"
        ]
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        valid_methods = []
        invalid_methods = []
        error_methods = []
        
        print(f"🧪 Testing {len(potential_wsmethods)} potential methods...")
        
        for i, wsmethod in enumerate(potential_wsmethods, 1):
            test_url = f"{endpoint_url}?action=wsmethod:{wsmethod}"
            
            print(f"  [{i:2d}/{len(potential_wsmethods)}] Testing: {wsmethod}", end=" ")
            
            try:
                # Test with minimal payload
                response = requests.post(
                    test_url,
                    json={"test": "discovery"},
                    headers=headers,
                    timeout=(3.05, 15)
                )
                
                if response.status_code == 200:
                    print("✅ VALID")
                    valid_methods.append(wsmethod)
                elif response.status_code in [400, 422]:
                    # Check if it's a method not found error vs validation error
                    try:
                        error_data = response.json()
                        if 'oslc:Error' in error_data:
                            error_msg = error_data['oslc:Error'].get('oslc:message', '')
                            if 'method' in error_msg.lower() and 'not found' in error_msg.lower():
                                print("❌ NOT FOUND")
                                invalid_methods.append(wsmethod)
                            else:
                                print("⚠️ VALIDATION ERROR (Method exists)")
                                valid_methods.append(wsmethod)
                        else:
                            print("⚠️ VALIDATION ERROR (Method exists)")
                            valid_methods.append(wsmethod)
                    except:
                        print("⚠️ VALIDATION ERROR (Method exists)")
                        valid_methods.append(wsmethod)
                else:
                    print(f"❓ HTTP {response.status_code}")
                    error_methods.append(wsmethod)
                    
            except Exception as e:
                print(f"💥 ERROR: {str(e)[:50]}")
                error_methods.append(wsmethod)
                
            # Small delay to avoid overwhelming the server
            time.sleep(0.1)
        
        return valid_methods, invalid_methods, error_methods
    
    def test_create_methods_detailed(self, valid_methods: List[str]) -> Dict:
        """Test create-related methods with more detailed payloads."""
        print(f"\n🔬 Detailed Testing of Create Methods")
        print("=" * 80)
        
        create_methods = [method for method in valid_methods if 'create' in method.lower()]
        
        if not create_methods:
            print("❌ No create methods found to test")
            return {}
        
        base_url = self.token_manager.base_url if hasattr(self.token_manager, 'base_url') else "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
        endpoint_url = f"{base_url}/api/os/mxapiasset"
        
        headers = {
            "Accept": "application/json",
            "Content-Type": "application/json",
            "apikey": self.api_key
        }
        
        results = {}
        
        for method in create_methods:
            print(f"\n🧪 Testing {method} with detailed payload...")
            
            test_url = f"{endpoint_url}?action=wsmethod:{method}"
            
            # Create method-specific payload
            if method == "createworkorder":
                payload = {
                    "assetnum": "TEST-ASSET-001",
                    "siteid": "LCVKWT",
                    "description": "Test work order creation from asset",
                    "worktype": "CM",
                    "priority": "3"
                }
            elif method == "createservicerequest":
                payload = {
                    "assetnum": "TEST-ASSET-001", 
                    "siteid": "LCVKWT",
                    "description": "Test service request creation from asset",
                    "priority": "3"
                }
            elif method == "createincident":
                payload = {
                    "assetnum": "TEST-ASSET-001",
                    "siteid": "LCVKWT", 
                    "description": "Test incident creation from asset",
                    "priority": "1"
                }
            else:
                payload = {
                    "assetnum": "TEST-ASSET-001",
                    "siteid": "LCVKWT",
                    "description": f"Test {method} from asset"
                }
            
            try:
                response = requests.post(
                    test_url,
                    json=payload,
                    headers=headers,
                    timeout=(5.0, 30)
                )
                
                results[method] = {
                    'status_code': response.status_code,
                    'payload_used': payload,
                    'response_preview': response.text[:500] if response.text else '',
                    'success': response.status_code in [200, 201]
                }
                
                print(f"   Status: {response.status_code}")
                if response.status_code in [200, 201]:
                    print("   ✅ SUCCESS")
                else:
                    print(f"   ❌ FAILED: {response.text[:100]}")
                    
            except Exception as e:
                results[method] = {
                    'status_code': None,
                    'payload_used': payload,
                    'error': str(e),
                    'success': False
                }
                print(f"   💥 ERROR: {str(e)}")
        
        return results

def main():
    """Main function to discover MXAPIASSET WSMethods."""
    print("🚀 MXAPIASSET WSMethod Discovery")
    print("=" * 80)
    
    discovery = MXAPIAssetWSMethodDiscovery()
    
    # Test both API endpoints
    base_url = "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo"
    endpoints = [
        f"{base_url}/api/os/mxapiasset",
        f"{base_url}/oslc/os/mxapiasset"
    ]
    
    all_results = {}
    
    for endpoint_url in endpoints:
        endpoint_type = "API" if "/api/" in endpoint_url else "OSLC"
        print(f"\n🔍 Testing {endpoint_type} endpoint...")
        
        try:
            valid_methods, invalid_methods, error_methods = discovery.discover_asset_wsmethods(endpoint_url)
            
            all_results[endpoint_type] = {
                'endpoint': endpoint_url,
                'valid_methods': valid_methods,
                'invalid_methods': invalid_methods,
                'error_methods': error_methods
            }
            
            print(f"\n📊 {endpoint_type} Results:")
            print(f"   ✅ Valid methods: {len(valid_methods)}")
            print(f"   ❌ Invalid methods: {len(invalid_methods)}")
            print(f"   💥 Error methods: {len(error_methods)}")
            
            if valid_methods:
                print(f"   🎯 Valid methods found: {', '.join(valid_methods)}")
                
                # Test create methods in detail
                create_results = discovery.test_create_methods_detailed(valid_methods)
                all_results[endpoint_type]['create_method_details'] = create_results
            
        except Exception as e:
            print(f"❌ Error testing {endpoint_type} endpoint: {str(e)}")
            all_results[endpoint_type] = {'error': str(e)}
    
    # Generate summary report
    print(f"\n📋 FINAL SUMMARY")
    print("=" * 80)
    
    for endpoint_type, results in all_results.items():
        if 'error' in results:
            print(f"{endpoint_type}: ❌ Error - {results['error']}")
        else:
            valid_count = len(results.get('valid_methods', []))
            print(f"{endpoint_type}: ✅ {valid_count} valid methods found")
            
            if results.get('valid_methods'):
                print(f"   Methods: {', '.join(results['valid_methods'])}")
    
    # Save detailed results
    with open('mxapiasset_wsmethod_discovery_results.json', 'w') as f:
        json.dump(all_results, f, indent=2)
    
    print(f"\n💾 Detailed results saved to: mxapiasset_wsmethod_discovery_results.json")

if __name__ == "__main__":
    main()
