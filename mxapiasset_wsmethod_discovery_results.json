{"API": {"endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/api/os/mxapiasset", "valid_methods": [], "invalid_methods": ["create", "createworkorder", "createservicerequest", "createincident", "createticket", "createwo", "createsr", "createpm", "createjob", "update", "delete", "sync", "merge", "validate", "move", "transfer", "changeowner", "changestatus", "changecondition", "retire", "decommission", "activate", "deactivate", "addchild", "removechild", "moveasset", "changeparent", "schedule", "unschedule", "calibrate", "inspect", "repair", "replace", "depreciate", "revalue", "capitalize", "adjustcost", "approve", "reject", "submit", "complete", "cancel", "hold", "resume", "gethistory", "getworkorders", "getservicerequests", "getincidents", "getmaintenance", "getcosts", "getdowntime", "addrelationship", "removerelationship", "linkasset", "unlinkasset", "addcondition", "updatecondition", "removecondition", "assesscondition", "addmeter", "updatemeter", "removemeter", "readmeter", "addsparepart", "<PERSON><PERSON><PERSON><PERSON>", "reservepart", "issuepart", "addwarranty", "updatewarranty", "removewarranty", "checkwarranty", "lockout", "tagout", "unlock", "untag", "safetycheckout", "safety<PERSON><PERSON>n"], "error_methods": []}, "OSLC": {"endpoint": "https://vectrustst01.manage.v2x.maximotest.gov2x.com/maximo/oslc/os/mxapiasset", "valid_methods": ["create", "createworkorder", "createservicerequest", "createincident", "createticket", "createwo", "createsr", "createpm", "createjob", "update", "delete", "sync", "merge", "validate", "move", "transfer", "changeowner", "changestatus", "changecondition", "retire", "decommission", "activate", "deactivate", "addchild", "removechild", "moveasset", "changeparent", "schedule", "unschedule", "calibrate", "inspect", "repair", "replace", "depreciate", "revalue", "capitalize", "adjustcost", "approve", "reject", "submit", "complete", "cancel", "hold", "resume", "gethistory", "getworkorders", "getservicerequests", "getincidents", "getmaintenance", "getcosts", "getdowntime", "addrelationship", "removerelationship", "linkasset", "unlinkasset", "addcondition", "updatecondition", "removecondition", "assesscondition", "addmeter", "updatemeter", "removemeter", "readmeter", "addsparepart", "<PERSON><PERSON><PERSON><PERSON>", "reservepart", "issuepart", "addwarranty", "updatewarranty", "removewarranty", "checkwarranty", "lockout", "tagout", "unlock", "untag", "safetycheckout", "safety<PERSON><PERSON>n"], "invalid_methods": [], "error_methods": [], "create_method_details": {"create": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test create from asset"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method create was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createworkorder": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test work order creation from asset", "worktype": "CM", "priority": "3"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createworkorder was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createservicerequest": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test service request creation from asset", "priority": "3"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createservicerequest was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createincident": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test incident creation from asset", "priority": "1"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createincident was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createticket": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test createticket from asset"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createticket was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createwo": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test createwo from asset"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createwo was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createsr": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test createsr from asset"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createsr was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createpm": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test createpm from asset"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createpm was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}, "createjob": {"status_code": 400, "payload_used": {"assetnum": "TEST-ASSET-001", "siteid": "LCVKWT", "description": "Test createjob from asset"}, "response_preview": "{\"oslc:Error\":{\"oslc:statusCode\":\"400\",\"spi:reasonCode\":\"BMXAA9372E\",\"oslc:message\":\"BMXAA9372E - The action method createjob was not found. Fix the name of the action to point to the right method name.\",\"oslc:extendedError\":{\"oslc:moreInfo\":{\"rdf:resource\":\"https:\\/\\/vectrus-mea.manage.v2x.maximotest.gov2x.com\\/maximo\\/api\\/error\\/messages\\/BMXAA9372E\"}}}}", "success": false}}}}