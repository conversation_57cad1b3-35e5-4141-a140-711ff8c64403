#!/usr/bin/env python3
"""
Test Cache Invalidation for Related Records

This script tests that newly created work orders appear in related records immediately.
"""

import requests
import json
import time

def test_cache_invalidation():
    """Test that cache invalidation works for newly created work orders."""
    
    base_url = "http://127.0.0.1:5010"
    
    print("🔄 TESTING CACHE INVALIDATION FOR RELATED RECORDS")
    print("=" * 60)
    
    # Use a known asset with existing work orders
    assetnum = "6468980"
    siteid = "LCVKWT"
    
    print(f"📋 Testing with asset: {assetnum} in site: {siteid}")
    
    try:
        # Step 1: Get initial related records count
        print("\n1️⃣ Step 1: Getting initial related records...")
        
        initial_response = requests.get(
            f"{base_url}/api/asset/related-records/{assetnum}?siteid={siteid}",
            timeout=30
        )
        
        if initial_response.status_code == 200:
            initial_data = initial_response.json()
            initial_wo_count = len(initial_data.get('workorders', []))
            print(f"✅ Initial work order count: {initial_wo_count}")
            
            # Show some existing work orders
            existing_wos = initial_data.get('workorders', [])[:3]
            if existing_wos:
                print("📋 Existing work orders (first 3):")
                for wo in existing_wos:
                    print(f"   • WO {wo.get('wonum', 'N/A')}: {wo.get('description', 'N/A')}")
        else:
            print(f"❌ Failed to get initial related records: {initial_response.status_code}")
            return
        
        # Step 2: Create a new work order
        print("\n2️⃣ Step 2: Creating a new work order...")
        
        form_data = {
            "assetnum": assetnum,
            "siteid": siteid,
            "orgid": "USARMY",
            "description": "Cache invalidation test work order",
            "worktype": "CM",
            "priority": "3",
            "longdescription": f"Testing cache invalidation at {time.strftime('%Y-%m-%d %H:%M:%S')}",
            "targstartdate": "2025-07-28T10:00",
            "createdby": "SOFG118757"
        }
        
        wo_response = requests.post(
            f"{base_url}/api/asset/create-workorder",
            json=form_data,
            timeout=30
        )
        
        if wo_response.status_code == 200:
            wo_result = wo_response.json()
            if wo_result.get('success'):
                new_wonum = wo_result.get('wonum')
                print(f"✅ Work order created successfully!")
                print(f"   • Work Order Number: {new_wonum}")
                print(f"   • Asset: {wo_result.get('assetnum')}")
                print(f"   • Site: {wo_result.get('siteid')}")
            else:
                print(f"❌ Work order creation failed: {wo_result.get('error')}")
                return
        else:
            print(f"❌ Work order creation request failed: {wo_response.status_code}")
            return
        
        # Step 3: Immediately check related records (should show new work order)
        print("\n3️⃣ Step 3: Checking related records immediately after creation...")
        
        # Small delay to ensure the cache invalidation has processed
        time.sleep(1)
        
        updated_response = requests.get(
            f"{base_url}/api/asset/related-records/{assetnum}?siteid={siteid}",
            timeout=30
        )
        
        if updated_response.status_code == 200:
            updated_data = updated_response.json()
            updated_wo_count = len(updated_data.get('workorders', []))
            print(f"✅ Updated work order count: {updated_wo_count}")
            
            # Check if the count increased
            if updated_wo_count > initial_wo_count:
                print(f"🎉 SUCCESS! Work order count increased from {initial_wo_count} to {updated_wo_count}")
                
                # Look for the new work order
                updated_wos = updated_data.get('workorders', [])
                new_wo_found = False
                
                for wo in updated_wos:
                    if wo.get('wonum') == new_wonum:
                        new_wo_found = True
                        print(f"✅ Found new work order in related records:")
                        print(f"   • WO Number: {wo.get('wonum')}")
                        print(f"   • Description: {wo.get('description')}")
                        print(f"   • Status: {wo.get('status')}")
                        print(f"   • Priority: {wo.get('priority')}")
                        break
                
                if new_wo_found:
                    print("🎯 CACHE INVALIDATION WORKING PERFECTLY!")
                else:
                    print("⚠️ Work order count increased but new WO not found in list")
                    
            elif updated_wo_count == initial_wo_count:
                print("❌ CACHE INVALIDATION FAILED: Work order count unchanged")
                print("   This suggests the cache was not invalidated properly")
                
                # Check if we're getting cached data
                cache_timestamp = updated_data.get('timestamp')
                initial_timestamp = initial_data.get('timestamp')
                
                if cache_timestamp == initial_timestamp:
                    print("🔍 DIAGNOSIS: Same timestamp - definitely using cached data")
                else:
                    print("🔍 DIAGNOSIS: Different timestamp - fresh data but WO not found")
                    
            else:
                print(f"🤔 UNEXPECTED: Work order count decreased from {initial_wo_count} to {updated_wo_count}")
                
        else:
            print(f"❌ Failed to get updated related records: {updated_response.status_code}")
        
        # Step 4: Test cache statistics
        print("\n4️⃣ Step 4: Checking cache statistics...")
        
        cache_response = requests.get(
            f"{base_url}/api/asset/cache-stats",
            timeout=10
        )
        
        if cache_response.status_code == 200:
            cache_stats = cache_response.json()
            print("📊 Cache Statistics:")
            print(json.dumps(cache_stats, indent=2))
        else:
            print(f"⚠️ Could not get cache statistics: {cache_response.status_code}")
            
    except Exception as e:
        print(f"❌ Test Error: {str(e)}")
    
    print("\n" + "=" * 60)
    print("🏁 CACHE INVALIDATION TEST COMPLETE")

if __name__ == "__main__":
    test_cache_invalidation()
