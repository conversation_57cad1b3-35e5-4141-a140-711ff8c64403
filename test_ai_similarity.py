#!/usr/bin/env python3
"""
Test AI Similarity Detection with High Duplicate Risk

This script tests the AI analysis with descriptions that should trigger high similarity warnings.
"""

import requests
import json

def test_high_similarity():
    """Test AI analysis with descriptions that should trigger high similarity detection."""
    
    base_url = "http://127.0.0.1:5010"
    
    print("🔍 TESTING AI SIMILARITY DETECTION")
    print("=" * 50)
    
    # Test Case 1: Very similar to existing work order "test from asset management sad"
    print("\n1️⃣ Testing HIGH SIMILARITY Detection:")
    print("Existing WO: 'test from asset management sad'")
    print("New Description: 'test from asset management system'")
    
    test_data = {
        "asset_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "Test Asset for AI Analysis"
        },
        "user_description": "test from asset management system",
        "form_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "test from asset management system",
            "worktype": "CM",
            "priority": "3"
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/ai-analysis/analyze-workorder",
            json=test_data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                kpis = result.get('kpis', {})
                duplication_analysis = result.get('duplication_analysis', {})
                
                print(f"✅ Analysis Results:")
                print(f"   • Risk Level: {kpis.get('risk_level', 'UNKNOWN')}")
                print(f"   • Highest Similarity: {round((kpis.get('highest_similarity_score', 0) * 100), 1)}%")
                print(f"   • Potential Duplicates: {kpis.get('potential_duplicates', 0)}")
                
                # Check for high-risk duplicates
                high_risk = duplication_analysis.get('high_risk', [])
                medium_risk = duplication_analysis.get('medium_risk', [])
                low_risk = duplication_analysis.get('low_risk', [])
                
                if high_risk:
                    print(f"\n🚨 HIGH RISK DUPLICATES ({len(high_risk)}):")
                    for dup in high_risk:
                        wo = dup['workorder']
                        similarity = round(dup['similarity_score'] * 100, 1)
                        print(f"   • WO {wo['wonum']}: {similarity}% similar")
                        print(f"     Description: '{wo['description']}'")
                        print(f"     Matching Keywords: {dup.get('matching_keywords', [])}")
                
                if medium_risk:
                    print(f"\n⚠️ MEDIUM RISK DUPLICATES ({len(medium_risk)}):")
                    for dup in medium_risk:
                        wo = dup['workorder']
                        similarity = round(dup['similarity_score'] * 100, 1)
                        print(f"   • WO {wo['wonum']}: {similarity}% similar")
                        print(f"     Description: '{wo['description']}'")
                
                if low_risk:
                    print(f"\n💡 LOW RISK SIMILAR ({len(low_risk)}):")
                    for dup in low_risk[:2]:  # Show first 2
                        wo = dup['workorder']
                        similarity = round(dup['similarity_score'] * 100, 1)
                        print(f"   • WO {wo['wonum']}: {similarity}% similar")
                        print(f"     Description: '{wo['description']}'")
                
                # Show recommendations
                recommendations = result.get('recommendations', [])
                if recommendations:
                    print(f"\n💡 AI Recommendations:")
                    for rec in recommendations:
                        print(f"   • [{rec['type']}] {rec['title']}")
                        print(f"     {rec['message']}")
            else:
                print(f"❌ Analysis Failed: {result.get('error')}")
        else:
            print(f"❌ Request Failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {str(e)}")
    
    # Test Case 2: Exact match test
    print("\n\n2️⃣ Testing EXACT MATCH Detection:")
    print("Existing WO: 'test from asset management sad'")
    print("New Description: 'test from asset management sad'")
    
    test_data2 = {
        "asset_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "Test Asset for AI Analysis"
        },
        "user_description": "test from asset management sad",
        "form_data": {
            "assetnum": "6468980",
            "siteid": "LCVKWT",
            "description": "test from asset management sad",
            "worktype": "CM",
            "priority": "3"
        }
    }
    
    try:
        response = requests.post(
            f"{base_url}/api/ai-analysis/analyze-workorder",
            json=test_data2,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                kpis = result.get('kpis', {})
                duplication_analysis = result.get('duplication_analysis', {})
                
                print(f"✅ Exact Match Test:")
                print(f"   • Risk Level: {kpis.get('risk_level', 'UNKNOWN')}")
                print(f"   • Highest Similarity: {round((kpis.get('highest_similarity_score', 0) * 100), 1)}%")
                print(f"   • Potential Duplicates: {kpis.get('potential_duplicates', 0)}")
                
                high_risk = duplication_analysis.get('high_risk', [])
                if high_risk:
                    print(f"\n🚨 EXACT DUPLICATE DETECTED:")
                    dup = high_risk[0]
                    wo = dup['workorder']
                    similarity = round(dup['similarity_score'] * 100, 1)
                    print(f"   • WO {wo['wonum']}: {similarity}% similar")
                    print(f"   • Status: {wo['status']}")
                    print(f"   • Risk Factors: {dup.get('risk_factors', [])}")
            else:
                print(f"❌ Exact Match Test Failed: {result.get('error')}")
        else:
            print(f"❌ Exact Match Request Failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Exact Match Error: {str(e)}")
    
    print("\n" + "=" * 50)
    print("🎯 SIMILARITY TESTING COMPLETE")

if __name__ == "__main__":
    test_high_similarity()
