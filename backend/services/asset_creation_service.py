#!/usr/bin/env python3
"""
Asset Creation Service

This service handles the creation of assets, work orders, service requests, 
incidents, and PM schedules using the appropriate Maximo REST API endpoints.

Based on investigation findings:
- MXAPIASSET does NOT have functional Create WSMethods
- Must use direct POST operations to appropriate endpoints:
  * Assets: POST /oslc/os/mxapiasset
  * Work Orders: POST /oslc/os/mxapiwodetail  
  * Service Requests: POST /oslc/os/mxapisr
  * Incidents: POST /oslc/os/mxapiincident (or similar)
  * PM Schedules: POST /oslc/os/mxapipm (or similar)

Author: Augment Agent
Date: 2025-01-27
"""

import logging
import requests
from typing import Dict, Any, Optional
import json
import base64
import re
from datetime import datetime

logger = logging.getLogger(__name__)

class AssetCreationService:
    """Service for creating assets and related records in Maximo."""
    
    def __init__(self, token_manager, related_records_service=None):
        """
        Initialize the Asset Creation Service.

        Args:
            token_manager: Token manager instance for authentication
            related_records_service: Optional related records service for cache invalidation
        """
        self.token_manager = token_manager
        self.logger = logger
        self.related_records_service = related_records_service

    def _decode_work_order_id(self, encoded_id: str) -> Optional[str]:
        """
        Attempt to decode a Maximo encoded work order ID to get the actual work order number.

        Args:
            encoded_id: The encoded work order ID (e.g., _TENWS1dULzE3Mzg1NDc3)

        Returns:
            The decoded work order number if successful, None otherwise
        """
        if not encoded_id or not encoded_id.startswith('_'):
            return None

        try:
            # Remove the leading underscore and try base64 decoding
            encoded_part = encoded_id[1:]  # Remove the '_' prefix

            # Try to decode as base64
            try:
                decoded_bytes = base64.b64decode(encoded_part + '==')  # Add padding if needed
                decoded_str = decoded_bytes.decode('utf-8')
                self.logger.info(f"🔓 DECODE: Decoded '{encoded_id}' to '{decoded_str}'")

                # Check if it's in the format SITEID/WONUM (e.g., LCVKWT/17385486)
                if '/' in decoded_str:
                    parts = decoded_str.split('/')
                    if len(parts) == 2:
                        site_part, wo_part = parts
                        # Validate that the work order part looks like a work order number
                        if wo_part.isdigit() and len(wo_part) >= 4:
                            self.logger.info(f"🎯 DECODE: Found WO# '{wo_part}' for site '{site_part}'")
                            return wo_part

                # Look for work order number patterns in the decoded string
                # Common patterns: numbers, alphanumeric codes
                wo_patterns = [
                    r'(\d{6,})',  # 6+ digit numbers
                    r'([A-Z]{2,}\d{4,})',  # Letters followed by numbers
                    r'(\d{4,}[A-Z]*)',  # Numbers followed by optional letters
                ]

                for pattern in wo_patterns:
                    match = re.search(pattern, decoded_str)
                    if match:
                        potential_wonum = match.group(1)
                        self.logger.info(f"🔓 DECODE: Pattern match WO# from '{encoded_id}': {potential_wonum}")
                        return potential_wonum

                self.logger.info(f"🔓 DECODE: Decoded '{encoded_id}' to '{decoded_str}' but no WO pattern found")
                return decoded_str  # Return the decoded string even if no pattern matches

            except Exception as decode_error:
                self.logger.warning(f"⚠️ DECODE: Base64 decode failed for '{encoded_id}': {decode_error}")
                return None

        except Exception as e:
            self.logger.warning(f"⚠️ DECODE: Failed to decode work order ID '{encoded_id}': {e}")
            return None

    def create_asset(self, asset_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new asset using MXAPIASSET endpoint.
        
        Args:
            asset_data: Asset data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 ASSET CREATION: Creating asset {asset_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiasset"
            
            # Prepare asset payload
            payload = {
                'assetnum': asset_data.get('assetnum'),
                'siteid': asset_data.get('siteid'),
                'description': asset_data.get('description'),
                'status': asset_data.get('status', 'OPERATING'),
                'location': asset_data.get('location', ''),
                'assettype': asset_data.get('assettype', 'PRODUCTION')
            }
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}
            
            response = self.token_manager.session.post(
                api_url,
                json=payload,
                timeout=(5.0, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code in [200, 201]:
                self.logger.info(f"✅ ASSET CREATION: Successfully created asset {asset_data.get('assetnum')}")
                return {
                    'success': True,
                    'message': f"Asset {asset_data.get('assetnum')} created successfully",
                    'data': response.json() if response.text else {}
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.logger.error(f"❌ ASSET CREATION: Failed - {error_msg}")
                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Asset creation failed: {str(e)}"
            self.logger.error(f"❌ ASSET CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def create_work_order(self, wo_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new work order using MXAPIWODETAIL endpoint.
        
        Args:
            wo_data: Work order data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 WORK ORDER CREATION: Creating work order for asset {wo_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapiwodetail"
            
            # Prepare work order payload
            payload = {
                'assetnum': wo_data.get('assetnum'),
                'siteid': wo_data.get('siteid'),
                'orgid': wo_data.get('orgid', 'USARMY'),  # Default to USARMY based on asset data
                'description': wo_data.get('description'),
                'worktype': wo_data.get('worktype', 'CM'),
                'priority': wo_data.get('priority', '3'),
                'status': 'WAPPR',  # Waiting for Approval
                'longdescription': wo_data.get('longdescription', ''),
                'reportedby': wo_data.get('createdby', ''),  # Map createdby to reportedby in Maximo
            }

            # Handle target start date formatting
            targstartdate = wo_data.get('targstartdate', '')
            if targstartdate:
                try:
                    # Convert from HTML datetime-local format (YYYY-MM-DDTHH:MM) to Maximo format
                    from datetime import datetime
                    dt = datetime.fromisoformat(targstartdate)
                    # Format for Maximo (ISO format with seconds)
                    payload['targstartdate'] = dt.strftime('%Y-%m-%dT%H:%M:%S')
                except ValueError:
                    self.logger.warning(f"⚠️ WORK ORDER CREATION: Invalid date format: {targstartdate}")
                    # Skip the date if it's invalid
                    pass
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}

            # Debug logging
            self.logger.info(f"🔧 WORK ORDER CREATION: Payload: {payload}")

            response = self.token_manager.session.post(
                api_url,
                json=payload,
                timeout=(5.0, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code in [200, 201]:
                # Check if response contains login page (authentication issue)
                if response.text and 'mas-login' in response.text:
                    error_msg = "Authentication session has expired. Please refresh the page and try again."
                    self.logger.error(f"❌ WORK ORDER CREATION: Authentication expired")
                    return {'success': False, 'error': error_msg}

                self.logger.info(f"✅ WORK ORDER CREATION: Successfully created work order for asset {wo_data.get('assetnum')}")

                # Try to extract work order number from various sources
                wonum = None
                response_data = {}

                # Method 1: Try to parse JSON response
                if response.text:
                    try:
                        response_data = response.json()
                        # Look for work order number in various possible fields
                        wonum = (response_data.get('wonum') or
                                response_data.get('workordernum') or
                                response_data.get('workorder') or
                                response_data.get('id'))
                    except ValueError as e:
                        self.logger.warning(f"⚠️ WORK ORDER CREATION: Invalid JSON response: {response.text[:200]}")
                        response_data = {'message': 'Work order created successfully (no response data)'}

                # Method 2: Try to extract from Location header
                if not wonum and 'Location' in response.headers:
                    location = response.headers['Location']
                    self.logger.info(f"🔍 WORK ORDER CREATION: Location header: {location}")
                    # Extract work order number from location URL
                    # Typical format: .../mxapiwodetail/WONUM
                    import re
                    match = re.search(r'/mxapiwodetail/([^/?]+)', location)
                    if match:
                        extracted_id = match.group(1)
                        self.logger.info(f"🎯 WORK ORDER CREATION: Extracted ID from Location: {extracted_id}")

                        # If it looks like an encoded ID, try to decode it immediately
                        if extracted_id.startswith('_'):
                            decoded_wonum = self._decode_work_order_id(extracted_id)
                            if decoded_wonum and decoded_wonum != extracted_id:
                                wonum = decoded_wonum
                                self.logger.info(f"🔓 WORK ORDER CREATION: Using decoded WO#: {wonum}")
                            else:
                                wonum = extracted_id
                                self.logger.info(f"⚠️ WORK ORDER CREATION: Could not decode, using raw ID: {wonum}")
                        else:
                            wonum = extracted_id

                # Method 3: Always try to make a follow-up request to get the actual work order details
                # This is important because we need the human-readable work order number
                if 'Location' in response.headers:
                    try:
                        location_url = response.headers['Location']
                        self.logger.info(f"🔍 WORK ORDER CREATION: Fetching details from: {location_url}")
                        detail_response = self.token_manager.session.get(location_url, timeout=15)
                        if detail_response.status_code == 200:
                            detail_data = detail_response.json()
                            # Get the actual human-readable work order number
                            actual_wonum = detail_data.get('wonum')
                            if actual_wonum:
                                self.logger.info(f"🎯 WORK ORDER CREATION: Got actual wonum from detail request: {actual_wonum}")
                                wonum = actual_wonum  # Use the actual work order number
                                response_data.update(detail_data)

                                # Log both the encoded ID and actual work order number for debugging
                                encoded_id = wonum if wonum and wonum.startswith('_') else 'N/A'
                                self.logger.info(f"📋 WORK ORDER CREATION: Encoded ID: {encoded_id}, Actual WO#: {actual_wonum}")
                        else:
                            self.logger.warning(f"⚠️ WORK ORDER CREATION: Detail request failed with status: {detail_response.status_code}")
                    except Exception as e:
                        self.logger.warning(f"⚠️ WORK ORDER CREATION: Could not fetch work order details: {str(e)}")

                # If we still don't have a work order number, try to decode the encoded ID
                if not wonum and 'Location' in response.headers:
                    location = response.headers['Location']
                    # Extract the encoded ID from the URL
                    encoded_match = re.search(r'/mxapiwodetail/([^/?]+)', location)
                    if encoded_match:
                        encoded_id = encoded_match.group(1)
                        decoded_wonum = self._decode_work_order_id(encoded_id)
                        if decoded_wonum:
                            wonum = decoded_wonum
                            self.logger.info(f"🔓 WORK ORDER CREATION: Used decoded WO#: {wonum}")

                # Add the work order number to response data
                if wonum:
                    response_data['wonum'] = wonum
                    response_data['workorder_number'] = wonum  # Alternative field name
                else:
                    # If we still don't have a work order number, use the encoded ID as fallback
                    if 'Location' in response.headers:
                        location = response.headers['Location']
                        encoded_match = re.search(r'/mxapiwodetail/([^/?]+)', location)
                        if encoded_match:
                            encoded_id = encoded_match.group(1)
                            response_data['encoded_id'] = encoded_id
                            response_data['wonum'] = encoded_id  # Use encoded ID as fallback
                            wonum = encoded_id
                            self.logger.warning(f"⚠️ WORK ORDER CREATION: Using encoded ID as fallback: {encoded_id}")

                # Invalidate related records cache for this asset
                if self.related_records_service:
                    try:
                        self.related_records_service.invalidate_asset_cache(wo_data.get('assetnum'), wo_data.get('siteid'))
                        self.logger.info(f"✅ WORK ORDER CREATION: Invalidated related records cache for asset {wo_data.get('assetnum')}")
                    except Exception as cache_error:
                        self.logger.warning(f"⚠️ WORK ORDER CREATION: Failed to invalidate cache: {cache_error}")
                else:
                    self.logger.warning("⚠️ WORK ORDER CREATION: No related records service available for cache invalidation")

                return {
                    'success': True,
                    'message': f"Work order created successfully for asset {wo_data.get('assetnum')}",
                    'data': response_data,
                    'wonum': wonum,  # Include at top level for easy access
                    'assetnum': wo_data.get('assetnum'),
                    'siteid': wo_data.get('siteid')
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.logger.error(f"❌ WORK ORDER CREATION: Failed - {error_msg}")

                # Provide user-friendly error messages
                if response.status_code == 400:
                    if 'BMXAA0090E' in response.text:
                        error_msg = "Invalid asset number or asset is not in operating status."
                    elif 'validation' in response.text.lower():
                        error_msg = "Validation error: Please check the provided data."
                elif response.status_code == 401:
                    error_msg = "Authentication failed. Please refresh the page and try again."
                elif response.status_code == 403:
                    error_msg = "Access denied. You may not have permission to create work orders."
                elif response.status_code == 500:
                    error_msg = "Server error. Please try again later."

                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Work order creation failed: {str(e)}"
            self.logger.error(f"❌ WORK ORDER CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def create_service_request(self, sr_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new service request using MXAPISR endpoint.
        
        Args:
            sr_data: Service request data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 SERVICE REQUEST CREATION: Creating service request for asset {sr_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            api_url = f"{base_url}/oslc/os/mxapisr"
            
            # Prepare service request payload
            payload = {
                'assetnum': sr_data.get('assetnum'),
                'siteid': sr_data.get('siteid'),
                'orgid': sr_data.get('orgid', 'USARMY'),  # Default to USARMY based on asset data
                'description': sr_data.get('description'),
                'priority': sr_data.get('priority', '3'),
                'status': 'NEW',
                'longdescription': sr_data.get('longdescription', ''),
                'requestedby': sr_data.get('requestedby', ''),
                'ownergroup': sr_data.get('ownergroup', 'MAINT')  # Default owner group
            }

            # Handle requested date formatting
            requesteddate = sr_data.get('requesteddate', '')
            if requesteddate:
                try:
                    # Convert from HTML datetime-local format (YYYY-MM-DDTHH:MM) to Maximo format
                    from datetime import datetime
                    dt = datetime.fromisoformat(requesteddate)
                    # Format for Maximo (ISO format with seconds)
                    payload['requesteddate'] = dt.strftime('%Y-%m-%dT%H:%M:%S')
                except ValueError:
                    self.logger.warning(f"⚠️ SERVICE REQUEST CREATION: Invalid date format: {requesteddate}")
                    # Skip the date if it's invalid
                    pass
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}

            # Debug logging
            self.logger.info(f"🔧 SERVICE REQUEST CREATION: Payload: {payload}")

            response = self.token_manager.session.post(
                api_url,
                json=payload,
                timeout=(5.0, 30),
                headers={
                    "Accept": "application/json",
                    "Content-Type": "application/json"
                }
            )
            
            if response.status_code in [200, 201]:
                # Check if response contains login page (authentication issue)
                if response.text and 'mas-login' in response.text:
                    error_msg = "Authentication session has expired. Please refresh the page and try again."
                    self.logger.error(f"❌ SERVICE REQUEST CREATION: Authentication expired")
                    return {'success': False, 'error': error_msg}

                self.logger.info(f"✅ SERVICE REQUEST CREATION: Successfully created service request for asset {sr_data.get('assetnum')}")

                # Safely parse JSON response
                response_data = {}
                if response.text:
                    try:
                        response_data = response.json()
                    except ValueError as e:
                        self.logger.warning(f"⚠️ SERVICE REQUEST CREATION: Invalid JSON response: {response.text[:200]}")
                        # If it's not JSON but successful, it might be a successful creation with no response body
                        response_data = {'message': 'Service request created successfully (no response data)'}

                return {
                    'success': True,
                    'message': f"Service request created successfully for asset {sr_data.get('assetnum')}",
                    'data': response_data
                }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                self.logger.error(f"❌ SERVICE REQUEST CREATION: Failed - {error_msg}")

                # Provide user-friendly error messages
                if response.status_code == 400:
                    if 'BMXAA0090E' in response.text:
                        error_msg = "Invalid asset number or asset is not in operating status."
                    elif 'validation' in response.text.lower():
                        error_msg = "Validation error: Please check the provided data."
                elif response.status_code == 401:
                    error_msg = "Authentication failed. Please refresh the page and try again."
                elif response.status_code == 403:
                    error_msg = "Access denied. You may not have permission to create service requests."
                elif response.status_code == 500:
                    error_msg = "Server error. Please try again later."

                return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Service request creation failed: {str(e)}"
            self.logger.error(f"❌ SERVICE REQUEST CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
    
    def create_incident(self, incident_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new incident using appropriate incident endpoint.
        
        Args:
            incident_data: Incident data including assetnum, siteid, description, etc.
            
        Returns:
            Dict containing success status and result data
        """
        try:
            self.logger.info(f"🔧 INCIDENT CREATION: Creating incident for asset {incident_data.get('assetnum')}")
            
            if not self.token_manager or not self.token_manager.session:
                return {'success': False, 'error': 'Not authenticated'}
            
            base_url = getattr(self.token_manager, 'base_url', '')
            # Try different possible incident endpoints
            possible_endpoints = [
                f"{base_url}/oslc/os/mxapiincident",
                f"{base_url}/oslc/os/mxapisr",  # Fallback to service request
                f"{base_url}/oslc/os/mxapiwodetail"  # Fallback to work order
            ]
            
            # Prepare incident payload
            payload = {
                'assetnum': incident_data.get('assetnum'),
                'siteid': incident_data.get('siteid'),
                'description': incident_data.get('description'),
                'priority': incident_data.get('priority', '1'),
                'status': 'NEW',
                'longdescription': incident_data.get('details', ''),
                'reportedby': incident_data.get('reportedby', ''),
                'reporteddate': incident_data.get('incidentdate', '')
            }
            
            # Remove empty values
            payload = {k: v for k, v in payload.items() if v}
            
            # Try each endpoint until one works
            for api_url in possible_endpoints:
                try:
                    response = self.token_manager.session.post(
                        api_url,
                        json=payload,
                        timeout=(5.0, 30),
                        headers={
                            "Accept": "application/json",
                            "Content-Type": "application/json"
                        }
                    )
                    
                    if response.status_code in [200, 201]:
                        endpoint_type = api_url.split('/')[-1]
                        self.logger.info(f"✅ INCIDENT CREATION: Successfully created incident using {endpoint_type}")
                        return {
                            'success': True,
                            'message': f"Incident created successfully for asset {incident_data.get('assetnum')} using {endpoint_type}",
                            'data': response.json() if response.text else {},
                            'endpoint_used': endpoint_type
                        }
                except Exception as e:
                    self.logger.warning(f"⚠️ INCIDENT CREATION: Failed with {api_url}: {str(e)}")
                    continue
            
            # If all endpoints failed
            error_msg = "All incident creation endpoints failed"
            self.logger.error(f"❌ INCIDENT CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
                
        except Exception as e:
            error_msg = f"Incident creation failed: {str(e)}"
            self.logger.error(f"❌ INCIDENT CREATION: {error_msg}")
            return {'success': False, 'error': error_msg}
