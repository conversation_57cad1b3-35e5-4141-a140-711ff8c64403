#!/usr/bin/env python3
"""
AI Work Order Analysis Service

This service provides AI-powered analysis for work order creation including:
- Duplication detection using NLP similarity analysis
- Existing work order insights and KPIs
- Smart recommendations and warnings
- Learning from historical data

Author: Augment Agent
Date: 2025-01-27
"""

import logging
import time
import re
import json
from typing import Dict, List, Any, Optional, Tu<PERSON>
from datetime import datetime, timed<PERSON><PERSON>
from difflib import SequenceMatcher
import sqlite3
import os

logger = logging.getLogger(__name__)

class AIWorkOrderAnalysisService:
    """Service for AI-powered work order analysis and duplication detection."""
    
    def __init__(self, token_manager):
        """
        Initialize the AI Work Order Analysis Service.
        
        Args:
            token_manager: Token manager instance for Maximo API authentication
        """
        self.token_manager = token_manager
        self.logger = logger
        
        # Initialize AI analysis database
        self._init_analysis_database()
        
        # NLP similarity thresholds
        self.HIGH_SIMILARITY_THRESHOLD = 0.85  # 85% similarity = high duplicate risk
        self.MEDIUM_SIMILARITY_THRESHOLD = 0.70  # 70% similarity = medium duplicate risk
        self.LOW_SIMILARITY_THRESHOLD = 0.50   # 50% similarity = low duplicate risk
        
        # Work order statuses to include in analysis (open work orders)
        self.OPEN_STATUSES = ['WAPPR', 'INPRG', 'WSCH', 'WMATL', 'WPCOND', 'APPR']
        
    def _init_analysis_database(self):
        """Initialize SQLite database for storing AI analysis results."""
        try:
            # Create database directory if it doesn't exist
            db_dir = os.path.join(os.path.dirname(__file__), '..', '..', 'data')
            os.makedirs(db_dir, exist_ok=True)
            
            self.db_path = os.path.join(db_dir, 'ai_workorder_analysis.db')
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Create AI analysis results table
                cursor.execute('''
                    CREATE TABLE IF NOT EXISTS ai_analysis_results (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        assetnum TEXT NOT NULL,
                        siteid TEXT NOT NULL,
                        user_description TEXT NOT NULL,
                        analysis_timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                        total_open_workorders INTEGER DEFAULT 0,
                        potential_duplicates INTEGER DEFAULT 0,
                        highest_similarity_score REAL DEFAULT 0.0,
                        analysis_duration_ms INTEGER DEFAULT 0,
                        user_decision TEXT,  -- 'proceed', 'cancel', 'view_existing'
                        created_workorder_num TEXT,
                        analysis_data TEXT  -- JSON string of full analysis results
                    )
                ''')
                
                # Create index for faster lookups
                cursor.execute('''
                    CREATE INDEX IF NOT EXISTS idx_asset_site 
                    ON ai_analysis_results(assetnum, siteid)
                ''')
                
                conn.commit()
                self.logger.info("✅ AI ANALYSIS: Database initialized successfully")
                
        except Exception as e:
            self.logger.error(f"❌ AI ANALYSIS: Database initialization failed: {e}")
    
    async def analyze_work_order_creation(self, asset_data: Dict[str, Any], user_description: str) -> Dict[str, Any]:
        """
        Perform comprehensive AI analysis for work order creation.
        
        Args:
            asset_data: Asset information (assetnum, siteid, etc.)
            user_description: User's intended work description
            
        Returns:
            Dict containing analysis results, insights, and recommendations
        """
        start_time = time.time()
        
        assetnum = asset_data.get('assetnum')
        siteid = asset_data.get('siteid')
        
        self.logger.info(f"🤖 AI ANALYSIS: Starting analysis for asset {assetnum} in site {siteid}")
        
        try:
            # Step 1: Fetch existing open work orders for the asset
            existing_workorders = await self._fetch_existing_workorders(assetnum, siteid)
            
            # Step 2: Perform AI duplication analysis
            duplication_analysis = self._analyze_duplications(user_description, existing_workorders)
            
            # Step 3: Generate insights and KPIs
            insights = self._generate_insights(existing_workorders, duplication_analysis)
            
            # Step 4: Create recommendations
            recommendations = self._generate_recommendations(duplication_analysis, existing_workorders)
            
            analysis_duration = int((time.time() - start_time) * 1000)  # Convert to milliseconds
            
            # Step 5: Prepare comprehensive analysis result
            analysis_result = {
                'success': True,
                'analysis_id': f"ai_{int(time.time())}_{assetnum}",
                'asset_info': {
                    'assetnum': assetnum,
                    'siteid': siteid,
                    'description': asset_data.get('description', '')
                },
                'user_description': user_description,
                'analysis_timestamp': datetime.now().isoformat(),
                'analysis_duration_ms': analysis_duration,
                'kpis': {
                    'total_open_workorders': len(existing_workorders),
                    'potential_duplicates': len(duplication_analysis['high_risk']) + len(duplication_analysis['medium_risk']),
                    'highest_similarity_score': duplication_analysis['highest_score'],
                    'risk_level': duplication_analysis['risk_level']
                },
                'existing_workorders': existing_workorders,
                'duplication_analysis': duplication_analysis,
                'insights': insights,
                'recommendations': recommendations
            }
            
            # Step 6: Store analysis results for learning
            await self._store_analysis_result(analysis_result)
            
            self.logger.info(f"✅ AI ANALYSIS: Completed in {analysis_duration}ms - {len(existing_workorders)} WOs, {duplication_analysis['risk_level']} risk")
            
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"❌ AI ANALYSIS: Analysis failed: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'analysis_duration_ms': int((time.time() - start_time) * 1000)
            }
    
    async def _fetch_existing_workorders(self, assetnum: str, siteid: str) -> List[Dict[str, Any]]:
        """Fetch existing open work orders for the specified asset using related records service."""
        try:
            # Import the asset related records service
            from backend.services.asset_related_records_service import AssetRelatedRecordsService

            # Initialize the related records service
            related_records_service = AssetRelatedRecordsService(self.token_manager)

            self.logger.info(f"📋 AI ANALYSIS: Fetching related records for asset {assetnum} in site {siteid}")

            # Get related records for the asset
            related_records = related_records_service.get_related_records(assetnum, siteid)

            # Check if there's an error in the response
            if 'error' in related_records:
                self.logger.warning(f"⚠️ AI ANALYSIS: Failed to get related records: {related_records.get('error', 'Unknown error')}")
                return []

            # Extract work orders from related records
            all_workorders = related_records.get('workorders', [])
            self.logger.info(f"📋 AI ANALYSIS: Retrieved {len(all_workorders)} total work orders from related records")

            # Filter for open work orders only
            open_workorders = []
            for wo in all_workorders:
                wo_status = wo.get('status', '')
                if wo_status in self.OPEN_STATUSES:
                    # Clean and format work order data
                    cleaned_wo = {
                        'wonum': wo.get('wonum', ''),
                        'description': wo.get('description', ''),
                        'longdescription': wo.get('longdescription', ''),
                        'status': wo_status,
                        'priority': wo.get('priority', ''),
                        'worktype': wo.get('worktype', ''),
                        'reportdate': wo.get('reportdate', ''),
                        'schedstart': wo.get('schedstart', ''),
                        'full_description': f"{wo.get('description', '')} {wo.get('longdescription', '')}".strip()
                    }
                    open_workorders.append(cleaned_wo)

            self.logger.info(f"📋 AI ANALYSIS: Found {len(open_workorders)} open work orders out of {len(all_workorders)} total for asset {assetnum}")
            return open_workorders

        except Exception as e:
            self.logger.error(f"❌ AI ANALYSIS: Error fetching work orders via related records: {str(e)}")
            return []

    def _analyze_duplications(self, user_description: str, existing_workorders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Analyze potential duplications using AI/NLP techniques.

        Args:
            user_description: User's intended work description
            existing_workorders: List of existing work orders

        Returns:
            Dict containing duplication analysis results
        """
        if not existing_workorders:
            return {
                'high_risk': [],
                'medium_risk': [],
                'low_risk': [],
                'highest_score': 0.0,
                'risk_level': 'LOW'
            }

        # Clean and normalize user description
        user_desc_clean = self._clean_description(user_description)

        high_risk_duplicates = []
        medium_risk_duplicates = []
        low_risk_duplicates = []
        highest_score = 0.0

        for wo in existing_workorders:
            # Combine description and long description for analysis
            wo_desc_clean = self._clean_description(wo['full_description'])

            # Calculate similarity scores using multiple methods
            similarity_scores = self._calculate_similarity_scores(user_desc_clean, wo_desc_clean)

            # Use the highest similarity score from all methods
            max_score = max(similarity_scores.values())
            highest_score = max(highest_score, max_score)

            # Create duplicate entry with analysis details
            duplicate_entry = {
                'workorder': wo,
                'similarity_score': max_score,
                'similarity_details': similarity_scores,
                'matching_keywords': self._find_matching_keywords(user_desc_clean, wo_desc_clean),
                'risk_factors': self._identify_risk_factors(wo, similarity_scores)
            }

            # Categorize by risk level
            if max_score >= self.HIGH_SIMILARITY_THRESHOLD:
                high_risk_duplicates.append(duplicate_entry)
            elif max_score >= self.MEDIUM_SIMILARITY_THRESHOLD:
                medium_risk_duplicates.append(duplicate_entry)
            elif max_score >= self.LOW_SIMILARITY_THRESHOLD:
                low_risk_duplicates.append(duplicate_entry)

        # Sort by similarity score (highest first)
        high_risk_duplicates.sort(key=lambda x: x['similarity_score'], reverse=True)
        medium_risk_duplicates.sort(key=lambda x: x['similarity_score'], reverse=True)
        low_risk_duplicates.sort(key=lambda x: x['similarity_score'], reverse=True)

        # Determine overall risk level
        if high_risk_duplicates:
            risk_level = 'HIGH'
        elif medium_risk_duplicates:
            risk_level = 'MEDIUM'
        elif low_risk_duplicates:
            risk_level = 'LOW'
        else:
            risk_level = 'NONE'

        return {
            'high_risk': high_risk_duplicates,
            'medium_risk': medium_risk_duplicates,
            'low_risk': low_risk_duplicates,
            'highest_score': highest_score,
            'risk_level': risk_level,
            'analysis_method': 'multi_algorithm_nlp'
        }

    def _clean_description(self, description: str) -> str:
        """Clean and normalize description text for analysis."""
        if not description:
            return ""

        # Convert to lowercase
        cleaned = description.lower()

        # Remove extra whitespace
        cleaned = re.sub(r'\s+', ' ', cleaned).strip()

        # Remove common stop words that don't add meaning
        stop_words = {'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'being'}
        words = cleaned.split()
        cleaned_words = [word for word in words if word not in stop_words]

        return ' '.join(cleaned_words)

    def _calculate_similarity_scores(self, desc1: str, desc2: str) -> Dict[str, float]:
        """Calculate similarity scores using multiple algorithms."""
        scores = {}

        # Method 1: Sequence Matcher (built-in Python)
        scores['sequence_matcher'] = SequenceMatcher(None, desc1, desc2).ratio()

        # Method 2: Word overlap ratio
        words1 = set(desc1.split())
        words2 = set(desc2.split())
        if words1 or words2:
            intersection = len(words1.intersection(words2))
            union = len(words1.union(words2))
            scores['word_overlap'] = intersection / union if union > 0 else 0.0
        else:
            scores['word_overlap'] = 0.0

        # Method 3: Jaccard similarity
        if words1 or words2:
            scores['jaccard'] = len(words1.intersection(words2)) / len(words1.union(words2)) if len(words1.union(words2)) > 0 else 0.0
        else:
            scores['jaccard'] = 0.0

        # Method 4: Keyword density similarity
        scores['keyword_density'] = self._calculate_keyword_density_similarity(desc1, desc2)

        return scores

    def _calculate_keyword_density_similarity(self, desc1: str, desc2: str) -> float:
        """Calculate similarity based on keyword density and importance."""
        # Define important maintenance keywords with weights
        important_keywords = {
            'repair': 2.0, 'fix': 2.0, 'replace': 2.0, 'install': 2.0, 'maintenance': 1.5,
            'inspect': 1.5, 'check': 1.5, 'clean': 1.5, 'service': 1.5, 'adjust': 1.5,
            'leak': 2.0, 'broken': 2.0, 'damaged': 2.0, 'faulty': 2.0, 'malfunction': 2.0,
            'noise': 1.5, 'vibration': 1.5, 'overheating': 2.0, 'failure': 2.0
        }

        # Calculate weighted keyword scores for each description
        def get_keyword_score(desc):
            words = desc.split()
            score = 0.0
            for word in words:
                if word in important_keywords:
                    score += important_keywords[word]
            return score

        score1 = get_keyword_score(desc1)
        score2 = get_keyword_score(desc2)

        if score1 == 0 and score2 == 0:
            return 0.0

        # Calculate similarity based on keyword scores
        max_score = max(score1, score2)
        min_score = min(score1, score2)

        return min_score / max_score if max_score > 0 else 0.0

    def _find_matching_keywords(self, desc1: str, desc2: str) -> List[str]:
        """Find matching keywords between two descriptions."""
        words1 = set(desc1.split())
        words2 = set(desc2.split())
        matching = words1.intersection(words2)

        # Filter out very short words (less than 3 characters)
        meaningful_matches = [word for word in matching if len(word) >= 3]

        return sorted(meaningful_matches)

    def _identify_risk_factors(self, workorder: Dict[str, Any], similarity_scores: Dict[str, float]) -> List[str]:
        """Identify specific risk factors for duplication."""
        risk_factors = []

        # High similarity risk
        if max(similarity_scores.values()) >= self.HIGH_SIMILARITY_THRESHOLD:
            risk_factors.append("Very high description similarity")

        # Same work type
        if workorder.get('worktype') in ['CM', 'EM']:  # Corrective/Emergency maintenance
            risk_factors.append("Same corrective maintenance type")

        # Recent work order
        if workorder.get('reportdate'):
            try:
                report_date = datetime.fromisoformat(workorder['reportdate'].replace('Z', '+00:00'))
                days_ago = (datetime.now() - report_date.replace(tzinfo=None)).days
                if days_ago <= 7:
                    risk_factors.append("Recent work order (within 7 days)")
                elif days_ago <= 30:
                    risk_factors.append("Recent work order (within 30 days)")
            except:
                pass

        # High priority
        if workorder.get('priority') in ['1', '2']:
            risk_factors.append("High priority work order")

        # Status-based risks
        status = workorder.get('status', '')
        if status == 'WAPPR':
            risk_factors.append("Waiting for approval - may be duplicate request")
        elif status == 'INPRG':
            risk_factors.append("Work in progress - may conflict with new request")

        return risk_factors

    def _generate_insights(self, existing_workorders: List[Dict[str, Any]], duplication_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Generate insights and KPIs from the analysis."""
        insights = {
            'asset_workload': self._analyze_asset_workload(existing_workorders),
            'status_distribution': self._analyze_status_distribution(existing_workorders),
            'priority_analysis': self._analyze_priority_distribution(existing_workorders),
            'recent_activity': self._analyze_recent_activity(existing_workorders),
            'duplication_risk_summary': self._summarize_duplication_risk(duplication_analysis)
        }

        return insights

    def _analyze_asset_workload(self, workorders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze the current workload for this asset."""
        total_open = len(workorders)

        workload_level = "LOW"
        if total_open >= 5:
            workload_level = "HIGH"
        elif total_open >= 3:
            workload_level = "MEDIUM"

        return {
            'total_open_workorders': total_open,
            'workload_level': workload_level,
            'message': f"Asset has {total_open} open work orders - {workload_level.lower()} workload"
        }

    def _analyze_status_distribution(self, workorders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze the distribution of work order statuses."""
        status_counts = {}
        for wo in workorders:
            status = wo.get('status', 'UNKNOWN')
            status_counts[status] = status_counts.get(status, 0) + 1

        return {
            'distribution': status_counts,
            'most_common': max(status_counts.items(), key=lambda x: x[1]) if status_counts else ('NONE', 0)
        }

    def _analyze_priority_distribution(self, workorders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze the priority distribution of existing work orders."""
        priority_counts = {'1': 0, '2': 0, '3': 0, '4': 0, '5': 0}

        for wo in workorders:
            priority = wo.get('priority', '3')
            if priority in priority_counts:
                priority_counts[priority] += 1

        high_priority_count = priority_counts['1'] + priority_counts['2']

        return {
            'distribution': priority_counts,
            'high_priority_count': high_priority_count,
            'has_high_priority': high_priority_count > 0
        }

    def _analyze_recent_activity(self, workorders: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze recent work order activity."""
        recent_count = 0
        very_recent_count = 0

        for wo in workorders:
            if wo.get('reportdate'):
                try:
                    report_date = datetime.fromisoformat(wo['reportdate'].replace('Z', '+00:00'))
                    days_ago = (datetime.now() - report_date.replace(tzinfo=None)).days

                    if days_ago <= 7:
                        very_recent_count += 1
                        recent_count += 1
                    elif days_ago <= 30:
                        recent_count += 1
                except:
                    pass

        return {
            'recent_30_days': recent_count,
            'very_recent_7_days': very_recent_count,
            'activity_level': 'HIGH' if very_recent_count >= 2 else 'MEDIUM' if recent_count >= 2 else 'LOW'
        }

    def _summarize_duplication_risk(self, duplication_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize the duplication risk assessment."""
        high_risk_count = len(duplication_analysis.get('high_risk', []))
        medium_risk_count = len(duplication_analysis.get('medium_risk', []))

        risk_summary = {
            'overall_risk': duplication_analysis.get('risk_level', 'NONE'),
            'high_risk_duplicates': high_risk_count,
            'medium_risk_duplicates': medium_risk_count,
            'highest_similarity': duplication_analysis.get('highest_score', 0.0)
        }

        # Generate risk message
        if high_risk_count > 0:
            risk_summary['message'] = f"⚠️ HIGH RISK: {high_risk_count} potential duplicate(s) detected"
        elif medium_risk_count > 0:
            risk_summary['message'] = f"⚠️ MEDIUM RISK: {medium_risk_count} similar work order(s) found"
        else:
            risk_summary['message'] = "✅ LOW RISK: No significant duplicates detected"

        return risk_summary

    def _generate_recommendations(self, duplication_analysis: Dict[str, Any], existing_workorders: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """Generate AI-powered recommendations based on analysis."""
        recommendations = []

        risk_level = duplication_analysis.get('risk_level', 'NONE')
        high_risk_count = len(duplication_analysis.get('high_risk', []))
        medium_risk_count = len(duplication_analysis.get('medium_risk', []))

        # Duplication-based recommendations
        if high_risk_count > 0:
            recommendations.append({
                'type': 'WARNING',
                'title': 'Potential Duplicate Detected',
                'message': f'Found {high_risk_count} work order(s) with very similar descriptions. Consider reviewing existing work orders before creating a new one.',
                'action': 'Review existing work orders'
            })

        if medium_risk_count > 0:
            recommendations.append({
                'type': 'CAUTION',
                'title': 'Similar Work Orders Found',
                'message': f'Found {medium_risk_count} work order(s) with similar descriptions. Verify if new work order is necessary.',
                'action': 'Check for overlapping work'
            })

        # Workload-based recommendations
        total_open = len(existing_workorders)
        if total_open >= 5:
            recommendations.append({
                'type': 'INFO',
                'title': 'High Asset Workload',
                'message': f'This asset has {total_open} open work orders. Consider prioritizing existing work.',
                'action': 'Review asset maintenance strategy'
            })

        # Priority-based recommendations
        high_priority_count = sum(1 for wo in existing_workorders if wo.get('priority') in ['1', '2'])
        if high_priority_count > 0:
            recommendations.append({
                'type': 'INFO',
                'title': 'High Priority Work Pending',
                'message': f'{high_priority_count} high-priority work order(s) are already open for this asset.',
                'action': 'Consider work order priority'
            })

        # If no issues found
        if not recommendations:
            recommendations.append({
                'type': 'SUCCESS',
                'title': 'Clear to Proceed',
                'message': 'No significant issues detected. Safe to create new work order.',
                'action': 'Proceed with creation'
            })

        return recommendations

    async def _store_analysis_result(self, analysis_result: Dict[str, Any]) -> None:
        """Store analysis results in database for future learning."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    INSERT INTO ai_analysis_results
                    (assetnum, siteid, user_description, analysis_timestamp, total_open_workorders,
                     potential_duplicates, highest_similarity_score, analysis_duration_ms, analysis_data)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    analysis_result['asset_info']['assetnum'],
                    analysis_result['asset_info']['siteid'],
                    analysis_result['user_description'],
                    analysis_result['analysis_timestamp'],
                    analysis_result['kpis']['total_open_workorders'],
                    analysis_result['kpis']['potential_duplicates'],
                    analysis_result['kpis']['highest_similarity_score'],
                    analysis_result['analysis_duration_ms'],
                    json.dumps(analysis_result)
                ))

                conn.commit()
                self.logger.info(f"💾 AI ANALYSIS: Stored analysis result for asset {analysis_result['asset_info']['assetnum']}")

        except Exception as e:
            self.logger.error(f"❌ AI ANALYSIS: Failed to store analysis result: {e}")

    async def update_user_decision(self, analysis_id: str, user_decision: str, created_workorder_num: str = None) -> None:
        """Update the user's decision in the analysis record."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Extract timestamp from analysis_id for lookup
                timestamp_part = analysis_id.split('_')[1] if '_' in analysis_id else None

                if timestamp_part:
                    cursor.execute('''
                        UPDATE ai_analysis_results
                        SET user_decision = ?, created_workorder_num = ?
                        WHERE analysis_timestamp LIKE ?
                    ''', (user_decision, created_workorder_num, f"%{timestamp_part}%"))

                    conn.commit()
                    self.logger.info(f"📝 AI ANALYSIS: Updated user decision: {user_decision}")

        except Exception as e:
            self.logger.error(f"❌ AI ANALYSIS: Failed to update user decision: {e}")

    def get_historical_insights(self, assetnum: str, siteid: str) -> Dict[str, Any]:
        """Get historical AI analysis insights for an asset."""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cursor.execute('''
                    SELECT COUNT(*) as total_analyses,
                           AVG(highest_similarity_score) as avg_similarity,
                           COUNT(CASE WHEN user_decision = 'proceed' THEN 1 END) as proceeded_count,
                           COUNT(CASE WHEN user_decision = 'cancel' THEN 1 END) as cancelled_count
                    FROM ai_analysis_results
                    WHERE assetnum = ? AND siteid = ?
                    AND analysis_timestamp >= datetime('now', '-90 days')
                ''', (assetnum, siteid))

                result = cursor.fetchone()

                if result:
                    return {
                        'total_analyses': result[0],
                        'average_similarity': result[1] or 0.0,
                        'proceeded_count': result[2],
                        'cancelled_count': result[3],
                        'cancellation_rate': result[3] / result[0] if result[0] > 0 else 0.0
                    }
                else:
                    return {'total_analyses': 0}

        except Exception as e:
            self.logger.error(f"❌ AI ANALYSIS: Failed to get historical insights: {e}")
            return {'error': str(e)}
